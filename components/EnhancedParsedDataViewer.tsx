'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Eye,
  Code,
  Copy,
  Check,
  Building,
  User,
  Package,
  DollarSign,
  Calendar,
  Hash,
} from 'lucide-react';

interface ParsedDataViewerProps {
  parsedData: {
    success: boolean;
    fileName: string;
    fileSize: number;
    pages: number;
    textLength: number;
    parsedData: any;
    rawText: string;
  };
}

export function EnhancedParsedDataViewer({ parsedData }: ParsedDataViewerProps) {
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('structured');

  const handleCopyJSON = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(parsedData.parsedData, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getDocumentTypeDisplay = (docType: string) => {
    const typeMap: { [key: string]: string } = {
      'TAX_INVOICE': 'Tax Invoice',
      'PURCHASE_ORDER': 'Purchase Order',
      'DELIVERY_NOTE': 'Delivery Note',
      'JOB_ORDER': 'Job Order',
      'UNKNOWN': 'Unknown Document Type',
    };

    return typeMap[docType] || docType.replace(/_/g, ' ').toUpperCase();
  };

  const renderValue = (value: any, key?: string): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-gray-400 italic">Not available</span>;
    }

    if (typeof value === 'boolean') {
      return (
        <Badge
          variant={value ? 'default' : 'secondary'}
          className="bg-blue-100 text-blue-800 border-blue-300"
        >
          {value.toString()}
        </Badge>
      );
    }

    if (typeof value === 'number') {
      // Format numbers based on context
      if (
        key?.toLowerCase().includes('amount') ||
        key?.toLowerCase().includes('rate') ||
        key?.toLowerCase().includes('cost')
      ) {
        return (
          <span className="text-green-700 font-mono font-semibold bg-green-50 px-2 py-1 rounded">
            ₹{value.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
          </span>
        );
      }
      if (key?.toLowerCase().includes('quantity') || key?.toLowerCase().includes('qty')) {
        return (
          <span className="text-blue-700 font-mono font-semibold bg-blue-50 px-2 py-1 rounded">
            {value.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
          </span>
        );
      }
      return (
        <span className="text-blue-700 font-mono font-semibold bg-blue-50 px-2 py-1 rounded">
          {value}
        </span>
      );
    }

    if (typeof value === 'string') {
      if (value === '') {
        return <span className="text-gray-500 italic">Empty</span>;
      }
      return (
        <span className="text-gray-900 break-words overflow-wrap-anywhere whitespace-pre-wrap">
          {value}
        </span>
      );
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-500 italic">No items</span>;
      }
      return (
        <div className="space-y-3 max-w-full">
          {value.map((item, index) => (
            <div
              key={index}
              className="border-l-4 border-blue-300 pl-4 bg-blue-50 rounded-r-lg py-2 overflow-hidden"
            >
              <div className="text-sm font-medium text-blue-800 mb-2">Item {index + 1}</div>
              <div className="overflow-hidden">
                {typeof item === 'object' ? renderObject(item) : renderValue(item)}
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (typeof value === 'object') {
      return renderObject(value);
    }

    return <span>{String(value)}</span>;
  };

  const renderObject = (obj: any) => {
    if (!obj || typeof obj !== 'object') return null;

    return (
      <div className="space-y-3 max-w-full">
        {Object.entries(obj).map(([key, value]) => (
          <div key={key} className="flex flex-col space-y-2 overflow-hidden">
            <div className="text-sm font-semibold text-gray-800 bg-gray-100 px-2 py-1 rounded break-words">
              {getFieldDisplayName(key)}:
            </div>
            <div className="ml-4 pl-2 border-l-2 border-gray-300 overflow-hidden">
              {renderValue(value, key)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const getFieldDisplayName = (key: string): string => {
    const fieldNames: { [key: string]: string } = {
      'InvoiceNo': 'Invoice Number',
      'InvoiceDate': 'Invoice Date',
      'DeliveryNote': 'Delivery Note',
      'DeliveryNoteDate': 'Delivery Note Date',
      'DeliveryNoteNo': 'Delivery Note Number',
      'PurchaseOrderNo': 'Purchase Order Number',
      'IRN': 'Invoice Reference Number (IRN)',
      'AckNo': 'Acknowledgment Number',
      'AckDate': 'Acknowledgment Date',
      'TotalAmount': 'Total Amount',
      'AmountInWords': 'Amount in Words',
      'TotalQuantity': 'Total Quantity',
      'GSTIN': 'GST Identification Number',
      'PAN': 'PAN Number',
      'HSN/SAC': 'HSN/SAC Code',
      'Warranty': 'Warranty Period',
      'SupportEmail': 'Support Email',
      'Jurisdiction': 'Jurisdiction',
      'Date': 'Date',
      'Remarks': 'Remarks',
      'Name': 'Name',
      'Address': 'Address',
      'Email': 'Email',
      'BankName': 'Bank Name',
      'AccountNumber': 'Account Number',
      'IFSC': 'IFSC Code',
      'DispatchedThrough': 'Dispatched Through',
      'Destination': 'Destination',
      'PaymentTerms': 'Payment Terms',
      'Description': 'Description',
      'Quantity': 'Quantity',
      'Unit': 'Unit',
      'Rate': 'Rate',
      'Amount': 'Amount',
      'CGST': 'CGST',
      'SGST': 'SGST',
      'IGST': 'IGST',
      'DeliveryChallan': 'Delivery Challan',
      'delivery_note_no': 'Delivery Note Number',
      'reference_no': 'Reference Number',
      'reference_date': 'Reference Date',
      'dispatch_doc_no': 'Dispatch Document Number',
      'dispatch_date': 'Dispatch Date',
      'dispatched_through': 'Dispatched Through',
      'payment_terms': 'Payment Terms',
      'destination': 'Destination',
      'document_type': 'Document Type',
      'company': 'Company',
      'address': 'Address',
      'gstin': 'GSTIN',
      'state': 'State',
      'email': 'Email',
      'consignee': 'Consignee',
      'buyer': 'Buyer',
      'items': 'Items',
      'total_amount': 'Total Amount',
      'HSN_SAC': 'HSN/SAC Code',
      'Details': 'Details',
      'BranchIFSC': 'Branch & IFSC',
      'AccountNo': 'Account Number',
      'StateCode': 'State Code',
    };

    return (
      fieldNames[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    );
  };

  const renderSection = (title: string, data: any, icon: React.ReactNode) => {
    if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) return null;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {typeof data === 'object' && !Array.isArray(data) ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(data).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <div className="text-sm font-medium text-gray-700">
                    {getFieldDisplayName(key)}
                  </div>
                  <div className="text-sm">{renderValue(value, key)}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-sm">{renderValue(data)}</div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderItemsTable = (items: any[]) => {
    if (!items || !Array.isArray(items) || items.length === 0) return null;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Package className="h-5 w-5 text-green-600" />
            Items ({items.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    #
                  </th>
                  {Object.keys(items[0] || {}).map(key => (
                    <th
                      key={key}
                      className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {getFieldDisplayName(key)}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {items.map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-3 py-2 text-sm font-medium text-gray-900">{index + 1}</td>
                    {Object.entries(item).map(([key, value]) => (
                      <td key={key} className="px-3 py-2 text-sm text-gray-900">
                        {renderValue(value, key)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="font-medium text-gray-900">Enhanced Parsed Data</h3>
              <p className="text-sm text-gray-600">
                {parsedData.pages} pages • {parsedData.textLength.toLocaleString()} characters
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant={parsedData.success ? 'default' : 'destructive'}
              className="bg-green-100 text-green-800 border-green-200"
            >
              {parsedData.success ? 'Success' : 'Failed'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyJSON}
              className="hover:bg-blue-50 hover:border-blue-300"
            >
              {copied ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <Copy className="h-4 w-4 text-blue-600" />
              )}
              {copied ? 'Copied!' : 'Copy JSON'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-2 mx-4 mb-0 bg-gray-100">
            <TabsTrigger
              value="structured"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-blue-50 text-gray-700"
            >
              <Eye className="h-4 w-4" />
              Structured View
            </TabsTrigger>
            <TabsTrigger
              value="raw"
              className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm hover:bg-blue-50 text-gray-700"
            >
              <Code className="h-4 w-4" />
              Raw JSON
            </TabsTrigger>
          </TabsList>

          <TabsContent value="structured" className="flex-1 overflow-auto m-0 p-4 bg-gray-50">
            <div className="space-y-4">
              {/* Document Type Badge */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <Hash className="h-5 w-5 text-blue-600" />
                  <span className="text-gray-800 font-medium">Document Type:</span>
                  <Badge className="bg-blue-600 text-white">
                    {getDocumentTypeDisplay(parsedData.parsedData.document_type || 'UNKNOWN')}
                  </Badge>
                </div>
              </div>

              {/* Document Numbers */}
              {(parsedData.parsedData.InvoiceNo ||
                parsedData.parsedData.DeliveryNote ||
                parsedData.parsedData.DeliveryNoteNo ||
                parsedData.parsedData.DeliveryChallan ||
                parsedData.parsedData.PurchaseOrderNo ||
                parsedData.parsedData.delivery_note_no ||
                parsedData.parsedData.DeliveryDetails?.InvoiceNo ||
                parsedData.parsedData.DeliveryDetails?.DeliveryNoteNo) &&
                renderSection(
                  'Document Numbers',
                  {
                    ...(parsedData.parsedData.InvoiceNo && {
                      InvoiceNo: parsedData.parsedData.InvoiceNo,
                    }),
                    ...(parsedData.parsedData.DeliveryNote && {
                      DeliveryNote: parsedData.parsedData.DeliveryNote,
                    }),
                    ...(parsedData.parsedData.DeliveryNoteNo && {
                      DeliveryNoteNo: parsedData.parsedData.DeliveryNoteNo,
                    }),
                    ...(parsedData.parsedData.DeliveryChallan && {
                      DeliveryChallan: parsedData.parsedData.DeliveryChallan,
                    }),
                    ...(parsedData.parsedData.PurchaseOrderNo && {
                      PurchaseOrderNo: parsedData.parsedData.PurchaseOrderNo,
                    }),
                    ...(parsedData.parsedData.delivery_note_no && {
                      'Delivery Note No': parsedData.parsedData.delivery_note_no,
                    }),
                    ...(parsedData.parsedData.DeliveryDetails?.InvoiceNo && {
                      'Invoice No (from Delivery Details)': parsedData.parsedData.DeliveryDetails.InvoiceNo,
                    }),
                    ...(parsedData.parsedData.DeliveryDetails?.DeliveryNoteNo && {
                      'Delivery Note No (from Delivery Details)': parsedData.parsedData.DeliveryDetails.DeliveryNoteNo,
                    }),
                    ...(parsedData.parsedData.IRN && { IRN: parsedData.parsedData.IRN }),
                    ...(parsedData.parsedData.AckNo && { AckNo: parsedData.parsedData.AckNo }),
                  },
                  <Hash className="h-5 w-5 text-blue-600" />
                )}

              {/* Dates */}
              {(parsedData.parsedData.InvoiceDate ||
                parsedData.parsedData.DeliveryDate ||
                parsedData.parsedData.DeliveryNoteDate ||
                parsedData.parsedData.OrderDate ||
                parsedData.parsedData.Date ||
                parsedData.parsedData.AckDate ||
                parsedData.parsedData.DeliveryDetails?.Date) &&
                renderSection(
                  'Dates',
                  {
                    ...(parsedData.parsedData.InvoiceDate && {
                      InvoiceDate: parsedData.parsedData.InvoiceDate,
                    }),
                    ...(parsedData.parsedData.DeliveryDate && {
                      DeliveryDate: parsedData.parsedData.DeliveryDate,
                    }),
                    ...(parsedData.parsedData.DeliveryNoteDate && {
                      DeliveryNoteDate: parsedData.parsedData.DeliveryNoteDate,
                    }),
                    ...(parsedData.parsedData.OrderDate && {
                      OrderDate: parsedData.parsedData.OrderDate,
                    }),
                    ...(parsedData.parsedData.Date && { Date: parsedData.parsedData.Date }),
                    ...(parsedData.parsedData.AckDate && {
                      AckDate: parsedData.parsedData.AckDate,
                    }),
                    ...(parsedData.parsedData.DeliveryDetails?.Date && {
                      'Delivery Date': parsedData.parsedData.DeliveryDetails.Date,
                    }),
                  },
                  <Calendar className="h-5 w-5 text-green-600" />
                )}

              {/* Company Information */}
              {parsedData.parsedData.Company &&
                renderSection(
                  'Company Information',
                  parsedData.parsedData.Company,
                  <Building className="h-5 w-5 text-blue-600" />
                )}

              {/* Job Order Information */}
              {parsedData.parsedData.JobOrder &&
                renderSection(
                  'Job Order Company',
                  parsedData.parsedData.JobOrder,
                  <Building className="h-5 w-5 text-indigo-600" />
                )}

              {/* Seller Information */}
              {parsedData.parsedData.Seller &&
                renderSection(
                  'Seller Information',
                  parsedData.parsedData.Seller,
                  <Building className="h-5 w-5 text-purple-600" />
                )}

              {/* Buyer Information */}
              {parsedData.parsedData.Buyer &&
                renderSection(
                  'Buyer Information',
                  parsedData.parsedData.Buyer,
                  <User className="h-5 w-5 text-orange-600" />
                )}

              {/* Supplier Information */}
              {parsedData.parsedData.Supplier &&
                renderSection(
                  'Supplier Information',
                  parsedData.parsedData.Supplier,
                  <Building className="h-5 w-5 text-green-600" />
                )}

              {/* Consignee Information */}
              {parsedData.parsedData.Consignee &&
                renderSection(
                  'Consignee Information',
                  parsedData.parsedData.Consignee,
                  <User className="h-5 w-5 text-teal-600" />
                )}

              {/* Items */}
              {parsedData.parsedData.Items && renderItemsTable(parsedData.parsedData.Items)}

              {/* Goods */}
              {parsedData.parsedData.Goods && renderItemsTable(parsedData.parsedData.Goods)}

              {/* Items (lowercase) */}
              {parsedData.parsedData.items && renderItemsTable(parsedData.parsedData.items)}

              {/* Financial Information */}
              {(parsedData.parsedData.TotalAmount ||
                parsedData.parsedData.AmountInWords ||
                parsedData.parsedData.total_amount) &&
                renderSection(
                  'Financial Summary',
                  {
                    ...(parsedData.parsedData.TotalAmount && {
                      TotalAmount: parsedData.parsedData.TotalAmount,
                    }),
                    ...(parsedData.parsedData.total_amount && {
                      'Total Amount': parsedData.parsedData.total_amount,
                    }),
                    ...(parsedData.parsedData.AmountInWords && {
                      AmountInWords: parsedData.parsedData.AmountInWords,
                    }),
                    ...(parsedData.parsedData.TotalQuantity && {
                      TotalQuantity: parsedData.parsedData.TotalQuantity,
                    }),
                  },
                  <DollarSign className="h-5 w-5 text-green-600" />
                )}

              {/* Tax Information */}
              {(parsedData.parsedData.Tax ||
                parsedData.parsedData.Taxes ||
                parsedData.parsedData.TaxDetails) &&
                renderSection(
                  'Tax Information',
                  parsedData.parsedData.Tax || parsedData.parsedData.Taxes || parsedData.parsedData.TaxDetails,
                  <DollarSign className="h-5 w-5 text-red-600" />
                )}

              {/* Dispatch Details */}
              {parsedData.parsedData.DispatchDetails &&
                renderSection(
                  'Dispatch Details',
                  parsedData.parsedData.DispatchDetails,
                  <Package className="h-5 w-5 text-purple-600" />
                )}

              {/* Delivery Information */}
              {parsedData.parsedData.DeliveryDetails &&
                renderSection(
                  'Delivery Information',
                  parsedData.parsedData.DeliveryDetails,
                  <Package className="h-5 w-5 text-blue-600" />
                )}

              {/* Bank Information */}
              {parsedData.parsedData.BankDetails &&
                renderSection(
                  'Bank Information',
                  parsedData.parsedData.BankDetails,
                  <Building className="h-5 w-5 text-emerald-600" />
                )}

              {/* Additional Information */}
              {(parsedData.parsedData.Warranty ||
                parsedData.parsedData.Jurisdiction ||
                parsedData.parsedData.SupportEmail) &&
                renderSection(
                  'Additional Information',
                  {
                    ...(parsedData.parsedData.Warranty && {
                      Warranty: parsedData.parsedData.Warranty,
                    }),
                    ...(parsedData.parsedData.Jurisdiction && {
                      Jurisdiction: parsedData.parsedData.Jurisdiction,
                    }),
                    ...(parsedData.parsedData.SupportEmail && {
                      SupportEmail: parsedData.parsedData.SupportEmail,
                    }),
                  },
                  <FileText className="h-5 w-5 text-gray-600" />
                )}

              {/* Dynamic Fields - Show any remaining fields */}
              {(() => {
                const handledFields = [
                  'document_type',
                  'rawText',
                  'processedAt',
                  'InvoiceNo',
                  'InvoiceDate',
                  'DeliveryNote',
                  'DeliveryNoteNo',
                  'DeliveryNoteDate',
                  'DeliveryChallan',
                  'DeliveryDate',
                  'PurchaseOrderNo',
                  'IRN',
                  'AckNo',
                  'AckDate',
                  'Date',
                  'OrderDate',
                  'Company',
                  'JobOrder',
                  'Seller',
                  'Buyer',
                  'Supplier',
                  'Consignee',
                  'Items',
                  'Goods',
                  'items',
                  'TotalAmount',
                  'total_amount',
                  'AmountInWords',
                  'TotalQuantity',
                  'Tax',
                  'Taxes',
                  'TaxDetails',
                  'DispatchDetails',
                  'DeliveryDetails',
                  'BankDetails',
                  'Warranty',
                  'Jurisdiction',
                  'SupportEmail',
                  'delivery_note_no',
                  'reference_no',
                  'reference_date',
                  'dispatch_doc_no',
                  'dispatch_date',
                  'dispatched_through',
                  'payment_terms',
                  'destination',
                  'company',
                  'address',
                  'gstin',
                  'state',
                  'email',
                  'consignee',
                  'buyer',
                ];

                const remainingFields = Object.entries(parsedData.parsedData).filter(
                  ([key, value]) =>
                    !handledFields.includes(key) &&
                    value !== null &&
                    value !== undefined &&
                    value !== '' &&
                    (typeof value !== 'object' || Object.keys(value).length > 0)
                );

                if (remainingFields.length > 0) {
                  return (
                    <Card className="mb-4">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <FileText className="h-5 w-5 text-gray-600" />
                          Other Fields
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {remainingFields.map(([key, value]) => (
                            <div key={key} className="border-l-4 border-gray-300 pl-4">
                              <div className="text-sm font-medium text-gray-700 mb-2">
                                {getFieldDisplayName(key)}
                              </div>
                              <div className="text-sm">{renderValue(value, key)}</div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  );
                }
                return null;
              })()}
            </div>
          </TabsContent>

          <TabsContent value="raw" className="flex-1 overflow-auto m-0 p-4 bg-gray-50">
            <pre className="bg-white border border-gray-300 p-4 rounded-lg overflow-auto text-sm font-mono whitespace-pre-wrap text-gray-900 shadow-sm h-full">
              {JSON.stringify(parsedData.parsedData, null, 2)}
            </pre>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}