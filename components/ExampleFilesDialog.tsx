'use client';

import { useState } from 'react';

interface ExampleFilesDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface DocumentType {
  name: string;
  description: string;
  exampleFile: string;
  fields: string[];
  color: string;
}

const documentTypes: DocumentType[] = [
  {
    name: 'Airtel Purchase Order',
    description: 'Purchase orders from Bharti Airtel Limited for power supply adaptors and UPS systems',
    exampleFile: '2.PO_3852_10000541_0_US.pdf',
    fields: ['PO_Number', 'Partner', 'Buyer', 'PO_Date', 'Total_Value', 'Items'],
    color: 'bg-red-100 text-red-800'
  },
  {
    name: 'Huhtamaki Purchase Order',
    description: 'Purchase orders from Huhtamaki India Limited for QR code labels',
    exampleFile: 'FLCN26PO024 - Huhtamaki V2.pdf',
    fields: ['Voucher_No', 'Buyer', 'Supplier', 'Date', 'Items', 'Total_Amount'],
    color: 'bg-blue-100 text-blue-800'
  },
  {
    name: 'Ingram Purchase Order',
    description: 'Purchase orders from Ingram Micro India for router UPS systems',
    exampleFile: 'IAPO_66-G3474_20250718_141420.pdf',
    fields: ['PO_Number', 'Vendor', 'Buyer', 'PO_Date', 'Delivery_Date', 'Payment_Terms', 'Items'],
    color: 'bg-green-100 text-green-800'
  },
  {
    name: 'Delivery Voucher',
    description: 'Delivery vouchers for shipped products with HSN codes',
    exampleFile: 'Delivery Voucher.pdf',
    fields: ['Delivery_Note_No', 'Buyer', 'Supplier', 'Date', 'Items'],
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    name: 'Resonate Job Order',
    description: 'Job orders from Resonate Systems for router UPS manufacturing',
    exampleFile: 'RSNT26J0018 - Resonate.pdf',
    fields: ['Delivery_Note_No', 'Buyer', 'Supplier', 'Date', 'Items'],
    color: 'bg-purple-100 text-purple-800'
  },
  {
    name: 'Ingram Delivery Note',
    description: 'Delivery notes for Ingram Micro shipments with dispatch details',
    exampleFile: 'RSNT26D0127 - Ingram 32.pdf',
    fields: ['Delivery_Note_No', 'Reference_No_Date', 'Buyers_Order_No', 'Dispatch_Doc_No', 'Items'],
    color: 'bg-indigo-100 text-indigo-800'
  },
  {
    name: 'Sales Invoice',
    description: 'Sales invoices with CGST/SGST calculations and payment terms',
    exampleFile: 'RSNT_SALES_INGRAM.pdf',
    fields: ['Invoice_No', 'Delivery_Note', 'Reference_No', 'Items', 'Total_Amount'],
    color: 'bg-pink-100 text-pink-800'
  },
  {
    name: 'Diligent Solutions Invoice',
    description: 'Tax invoices for Diligent Solutions with IGST calculations',
    exampleFile: 'RSNT26T0122 - Diligent Solutions.pdf',
    fields: ['Invoice_No', 'Delivery_Note', 'Buyer', 'Items', 'IGST', 'Total_Amount'],
    color: 'bg-teal-100 text-teal-800'
  },
  {
    name: 'Arcsys Invoice',
    description: 'Tax invoices for Arcsys Techsolutions with dispatch details',
    exampleFile: 'RSNT26T0147 - Arcsys.pdf',
    fields: ['Invoice_No', 'Delivery_Note', 'Buyer', 'Items', 'IGST', 'Total_Amount'],
    color: 'bg-orange-100 text-orange-800'
  }
];

export default function ExampleFilesDialog({ isOpen, onClose }: ExampleFilesDialogProps) {
  const [selectedType, setSelectedType] = useState<DocumentType | null>(null);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Supported PDF Formats & Examples</h2>
              <p className="text-blue-100 mt-1">View example files and supported document types</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Left Panel - Document Types */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Types ({documentTypes.length})</h3>
              <div className="space-y-3">
                {documentTypes.map((type, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
                      selectedType?.name === type.name
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedType(type)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${type.color}`}>
                            {type.name}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                        <div className="flex items-center text-xs text-gray-500">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          {type.exampleFile}
                        </div>
                      </div>
                      <div className="ml-2">
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Panel - Document Details */}
          <div className="w-1/2 overflow-y-auto">
            <div className="p-6">
              {selectedType ? (
                <div>
                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedType.color}`}>
                        {selectedType.name}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Document Details</h3>
                    <p className="text-gray-600 mb-4">{selectedType.description}</p>
                    
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="flex items-center gap-2 mb-2">
                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="font-medium text-gray-900">Example File:</span>
                      </div>
                      <p className="text-sm text-gray-700 font-mono bg-white px-3 py-2 rounded border">
                        {selectedType.exampleFile}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Extracted Fields</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {selectedType.fields.map((field, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border"
                        >
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="font-mono text-sm text-gray-800">{field}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <h5 className="font-medium text-blue-900 mb-1">Parser Information</h5>
                        <p className="text-sm text-blue-800">
                          This document type is automatically detected and parsed using dynamic logic that handles both vertical and horizontal text orders. 
                          The parser extracts all the fields shown above with high accuracy.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Document Type</h3>
                  <p className="text-gray-600">
                    Click on any document type from the left panel to view its details, example file, and extracted fields.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <span className="font-medium">{documentTypes.length}</span> document types supported with 100% accuracy parsing
            </div>
            <button
              onClick={onClose}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
