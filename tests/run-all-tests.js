const { OptimizedParserTest } = require('./optimized-parser-test');

/**
 * Test Runner for All PDF Parsers
 * Compares different parser implementations and validates against expected JSON
 */

async function runComprehensiveTests() {
  console.log('🎯 PDF Parser Comprehensive Test Suite');
  console.log('=====================================\n');

  const testSuite = new OptimizedParserTest();
  
  // Test with different parser implementations
  const parsers = [
    {
      name: 'Comprehensive Parser',
      module: () => require('../lib/comprehensive-pdf-parser').ComprehensivePDFParser
    },
    {
      name: 'Simple Parser', 
      module: () => require('../lib/simple-pdf-parser').SimplePDFParser
    },
    {
      name: 'Enhanced Parser',
      module: () => require('../lib/enhanced-pdf-parser').EnhancedPDFParser
    }
  ];

  const allResults = {};

  for (const parserConfig of parsers) {
    try {
      console.log(`\n🔧 Testing ${parserConfig.name}`);
      console.log('='.repeat(40));
      
      const Parser = parserConfig.module();
      const results = await testSuite.runAllTests(Parser);
      
      allResults[parserConfig.name] = {
        successRate: (results.passed / results.total) * 100,
        passed: results.passed,
        failed: results.failed,
        total: results.total,
        details: results.details
      };
      
    } catch (error) {
      console.error(`❌ Failed to test ${parserConfig.name}:`, error.message);
      allResults[parserConfig.name] = {
        error: error.message,
        successRate: 0,
        passed: 0,
        failed: 0,
        total: 0
      };
    }
  }

  // Print comparison summary
  console.log('\n' + '='.repeat(80));
  console.log('📊 PARSER COMPARISON SUMMARY');
  console.log('='.repeat(80));
  
  Object.entries(allResults).forEach(([name, results]) => {
    if (results.error) {
      console.log(`${name}: ERROR - ${results.error}`);
    } else {
      console.log(`${name}: ${results.successRate.toFixed(1)}% (${results.passed}/${results.total})`);
    }
  });

  // Find best performing parser
  const bestParser = Object.entries(allResults)
    .filter(([_, results]) => !results.error)
    .sort((a, b) => b[1].successRate - a[1].successRate)[0];

  if (bestParser) {
    console.log(`\n🏆 Best Performing Parser: ${bestParser[0]} (${bestParser[1].successRate.toFixed(1)}%)`);
  }

  // Save comprehensive results
  const fs = require('fs');
  const comprehensiveResults = {
    timestamp: new Date().toISOString(),
    summary: allResults,
    bestParser: bestParser ? bestParser[0] : null,
    testConfiguration: {
      totalPDFs: Object.values(allResults)[0]?.total || 0,
      expectedOutputsLoaded: true,
      testTypes: ['Field Validation', 'Structure Validation', 'Data Type Validation']
    }
  };

  try {
    fs.writeFileSync('tests/comprehensive-test-results.json', JSON.stringify(comprehensiveResults, null, 2));
    console.log('\n📝 Comprehensive results saved to: tests/comprehensive-test-results.json');
  } catch (error) {
    console.error('Failed to save comprehensive results:', error.message);
  }

  return comprehensiveResults;
}

// Individual test functions for specific document types
async function testSpecificDocumentType(docType, parser) {
  const testSuite = new OptimizedParserTest();
  const fs = require('fs');
  const path = require('path');
  
  const pdfFiles = fs.readdirSync('./example file')
    .filter(file => file.toLowerCase().endsWith('.pdf'));
  
  const relevantFiles = pdfFiles.filter(file => {
    const fileName = file.toLowerCase();
    switch (docType) {
      case 'AIRTEL_PO':
        return fileName.includes('po_3852');
      case 'HUHTAMAKI_PO':
        return fileName.includes('huhtamaki');
      case 'INGRAM_PO':
        return fileName.includes('iapo');
      case 'DELIVERY_VOUCHER':
        return fileName.includes('delivery voucher');
      case 'RESONATE_JOB_ORDER':
        return fileName.includes('rsnt26j');
      case 'INGRAM_DELIVERY':
        return fileName.includes('ingram') && fileName.includes('32');
      case 'SALES_INVOICE':
        return fileName.includes('sales');
      case 'DILIGENT_INVOICE':
        return fileName.includes('diligent');
      case 'ARCSYS_INVOICE':
        return fileName.includes('arcsys');
      default:
        return false;
    }
  });

  console.log(`\n🎯 Testing ${docType} with ${relevantFiles.length} files`);
  
  const results = [];
  for (const file of relevantFiles) {
    const result = await testSuite.runSingleTest(file, parser);
    results.push(result);
  }

  return results;
}

// Validation checklist generator
function generateValidationChecklist() {
  const checklist = {
    'Airtel PO': [
      'PO_Number extraction (BAL-EGB-ISP format)',
      'Partner name identification',
      'Buyer name identification', 
      'PO_Date parsing (DD-MMM-YY format)',
      'Total_Value with currency',
      'Items array with Description, Quantity, Unit_Price, Line_Total'
    ],
    'Huhtamaki PO': [
      'Voucher_No extraction (FLCN format)',
      'Buyer and Supplier identification',
      'Date parsing',
      'Items with Description, Quantity, Rate',
      'Total_Amount with currency'
    ],
    'Ingram PO': [
      'PO_Number extraction',
      'Vendor and Buyer identification',
      'Date fields (PO_Date, Delivery_Date)',
      'Payment_Terms extraction',
      'Items with Line, Quantity, Unit, SKU, Description, HSN, Rate, Extended_Cost, GST',
      'Total_GST and Grand_Total calculations'
    ],
    'Delivery Voucher': [
      'Delivery_Note_No extraction',
      'Buyer and Supplier identification',
      'Date parsing',
      'Items with Description, Quantity, HSN'
    ],
    'Resonate Job Order': [
      'Delivery_Note_No extraction',
      'Buyer and Supplier identification',
      'Date parsing',
      'Items with Description, Quantity, HSN'
    ],
    'Ingram Delivery': [
      'Delivery_Note_No extraction',
      'Reference_No_Date parsing',
      'Buyers_Order_No extraction',
      'Dispatch details',
      'Items with Description, Quantity, HSN, Tax'
    ],
    'Sales Invoice': [
      'Invoice_No extraction',
      'Delivery_Note extraction',
      'Reference_No extraction',
      'Dispatch details',
      'Payment terms',
      'Items with rates and taxes',
      'Total_Amount calculation'
    ],
    'Diligent Invoice': [
      'Invoice_No extraction',
      'Delivery_Note extraction',
      'Buyer and Supplier identification',
      'Items with Description, Quantity, Rate, Amount, HSN',
      'IGST calculation',
      'Total_Amount calculation'
    ],
    'Arcsys Invoice': [
      'Invoice_No extraction',
      'Delivery_Note extraction',
      'Buyer and Supplier identification',
      'Items with Description, Quantity, Rate, Amount, HSN',
      'IGST calculation',
      'Total_Amount with currency format'
    ]
  };

  console.log('\n📋 VALIDATION CHECKLIST');
  console.log('='.repeat(50));
  
  Object.entries(checklist).forEach(([docType, checks]) => {
    console.log(`\n${docType}:`);
    checks.forEach((check, index) => {
      console.log(`  ${index + 1}. ${check}`);
    });
  });

  return checklist;
}

// Run tests if called directly
if (require.main === module) {
  runComprehensiveTests()
    .then(results => {
      console.log('\n🎯 All tests completed!');
      
      // Generate validation checklist
      generateValidationChecklist();
      
      // Exit with appropriate code
      const hasFailures = Object.values(results.summary).some(r => r.failed > 0 || r.error);
      process.exit(hasFailures ? 1 : 0);
    })
    .catch(error => {
      console.error('💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { 
  runComprehensiveTests, 
  testSpecificDocumentType, 
  generateValidationChecklist 
};
