const fs = require('fs');
const path = require('path');
const pdf = require('pdf-parse');

// Test configuration
const EXAMPLE_FOLDER = './example file';
const EXPECTED_JSON_PATH = './example file/pdf_Expected_json_output.json';

/**
 * Optimized PDF Parser Test Suite
 * Tests parser output against expected JSON structure for 100% accuracy
 */
class OptimizedParserTest {
  constructor() {
    this.expectedOutputs = this.loadExpectedOutputs();
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: [],
      details: []
    };
  }

  loadExpectedOutputs() {
    try {
      const jsonContent = fs.readFileSync(EXPECTED_JSON_PATH, 'utf8');
      return JSON.parse(jsonContent);
    } catch (error) {
      console.error('Failed to load expected outputs:', error.message);
      return {};
    }
  }

  async extractPDFText(pdfPath) {
    try {
      const dataBuffer = fs.readFileSync(pdfPath);
      const data = await pdf(dataBuffer, {
        normalizeWhitespace: true,
        disableCombineTextItems: false,
      });
      return data.text;
    } catch (error) {
      console.error(`Failed to extract text from ${pdfPath}:`, error.message);
      return null;
    }
  }

  compareResults(extracted, expected, pdfName) {
    const differences = [];

    const compareObjects = (extractedObj, expectedObj, path = '') => {
      if (expectedObj === null || expectedObj === undefined) return;
      
      if (Array.isArray(expectedObj)) {
        if (!Array.isArray(extractedObj)) {
          differences.push({
            path,
            expected: `Array with ${expectedObj.length} items`,
            extracted: typeof extractedObj,
            type: 'type_mismatch'
          });
          return;
        }

        expectedObj.forEach((expectedItem, index) => {
          if (extractedObj[index] !== undefined) {
            compareObjects(extractedObj[index], expectedItem, `${path}[${index}]`);
          } else {
            differences.push({
              path: `${path}[${index}]`,
              expected: expectedItem,
              extracted: 'MISSING',
              type: 'missing_array_item'
            });
          }
        });
        return;
      }

      if (typeof expectedObj === 'object') {
        if (typeof extractedObj !== 'object' || Array.isArray(extractedObj)) {
          differences.push({
            path,
            expected: 'Object',
            extracted: typeof extractedObj,
            type: 'type_mismatch'
          });
          return;
        }

        Object.keys(expectedObj).forEach(key => {
          const expectedValue = expectedObj[key];
          const extractedValue = extractedObj[key];
          const currentPath = path ? `${path}.${key}` : key;

          if (extractedValue === undefined) {
            differences.push({
              path: currentPath,
              expected: expectedValue,
              extracted: 'MISSING',
              type: 'missing_field'
            });
          } else if (typeof expectedValue === 'object' && expectedValue !== null) {
            compareObjects(extractedValue, expectedValue, currentPath);
          } else if (extractedValue !== expectedValue) {
            differences.push({
              path: currentPath,
              expected: expectedValue,
              extracted: extractedValue,
              type: 'value_mismatch'
            });
          }
        });
        return;
      }

      // Primitive values
      if (extractedObj !== expectedObj) {
        differences.push({
          path: path || 'root',
          expected: expectedObj,
          extracted: extractedObj,
          type: 'value_mismatch'
        });
      }
    };

    compareObjects(extracted, expected);
    return differences;
  }

  findExpectedOutput(pdfName) {
    // Direct mapping based on PDF filename
    const fileName = pdfName.replace('.pdf', '');
    
    if (this.expectedOutputs[pdfName]) {
      return this.expectedOutputs[pdfName];
    }

    // Fallback mapping for specific files
    const mappings = {
      '2.PO_3852_10000541_0_US': '2.PO_3852_10000541_0_US.pdf',
      'Delivery Voucher': 'Delivery Voucher.pdf',
      'FLCN26PO024 - Huhtamaki V2': 'FLCN26PO024 - Huhtamaki V2.pdf',
      'IAPO_66-G3474_20250718_141420': 'IAPO_66-G3474_20250718_141420.pdf',
      'RSNT_SALES_INGRAM': 'RSNT_SALES_INGRAM.pdf',
      'RSNT26D0127 - Ingram 32': 'RSNT26D0127 - Ingram 32.pdf',
      'RSNT26J0018 - Resonate': 'RSNT26J0018 - Resonate.pdf',
      'RSNT26J0022 - Resonate': 'RSNT26J0022 - Resonate.pdf',
      'RSNT26T0122 - Diligent Solutions': 'RSNT26T0122 - Diligent Solutions.pdf',
      'RSNT26T0129 - Ingram 29': 'RSNT26T0129 - Ingram 29 2.pdf',
      'RSNT26T0147 - Arcsys': 'RSNT26T0147 - Arcsys.pdf'
    };

    const mappedKey = mappings[fileName];
    if (mappedKey && this.expectedOutputs[mappedKey]) {
      return this.expectedOutputs[mappedKey];
    }

    return null;
  }

  async runSingleTest(pdfFile, parser) {
    console.log(`\n📄 Testing: ${pdfFile}`);
    console.log('─'.repeat(50));

    try {
      const pdfPath = path.join(EXAMPLE_FOLDER, pdfFile);
      const pdfText = await this.extractPDFText(pdfPath);

      if (!pdfText) {
        throw new Error('Failed to extract PDF text');
      }

      const extractedData = parser.parseDocument(pdfText);
      const expectedData = this.findExpectedOutput(pdfFile);

      if (!expectedData) {
        console.log('⚠️  No expected output found for this PDF');
        return {
          status: 'NO_EXPECTED_DATA',
          pdf: pdfFile,
          extracted: extractedData
        };
      }

      const differences = this.compareResults(extractedData, expectedData, pdfFile);

      if (differences.length === 0) {
        console.log('✅ PASSED - Perfect match with expected output');
        this.testResults.passed++;
        return {
          status: 'PASSED',
          pdf: pdfFile,
          extracted: extractedData
        };
      } else {
        console.log(`❌ FAILED - ${differences.length} differences found:`);
        differences.slice(0, 5).forEach(diff => {
          console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
        });
        if (differences.length > 5) {
          console.log(`   ... and ${differences.length - 5} more differences`);
        }
        
        this.testResults.failed++;
        return {
          status: 'FAILED',
          pdf: pdfFile,
          differences,
          extracted: extractedData,
          expected: expectedData
        };
      }
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push({ pdf: pdfFile, error: error.message });
      return {
        status: 'ERROR',
        pdf: pdfFile,
        error: error.message
      };
    }
  }

  async runAllTests(parser) {
    console.log('🚀 Starting Optimized PDF Parser Test Suite\n');
    
    const pdfFiles = fs.readdirSync(EXAMPLE_FOLDER)
      .filter(file => file.toLowerCase().endsWith('.pdf'));
    
    console.log(`📁 Found ${pdfFiles.length} PDF files to test\n`);
    
    this.testResults.total = pdfFiles.length;

    for (const pdfFile of pdfFiles) {
      const result = await this.runSingleTest(pdfFile, parser);
      this.testResults.details.push(result);
    }

    this.printSummary();
    this.saveResults();
    
    return this.testResults;
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed} ✅`);
    console.log(`Failed: ${this.testResults.failed} ❌`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);

    if (this.testResults.failed > 0) {
      console.log('\n🔍 FAILED TESTS:');
      this.testResults.details
        .filter(result => result.status === 'FAILED')
        .forEach(result => {
          console.log(`\n📄 ${result.pdf}:`);
          if (result.differences) {
            result.differences.slice(0, 3).forEach(diff => {
              console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
            });
          }
        });
    }

    if (this.testResults.errors.length > 0) {
      console.log('\n💥 ERRORS:');
      this.testResults.errors.forEach(error => {
        console.log(`   ${error.pdf}: ${error.error}`);
      });
    }
  }

  saveResults() {
    const resultsFile = 'tests/test-results-optimized.json';
    try {
      // Ensure tests directory exists
      if (!fs.existsSync('tests')) {
        fs.mkdirSync('tests');
      }
      
      fs.writeFileSync(resultsFile, JSON.stringify(this.testResults, null, 2));
      console.log(`\n📝 Detailed results saved to: ${resultsFile}`);
    } catch (error) {
      console.error('Failed to save results:', error.message);
    }
  }

  // Validation methods for specific document types
  validateAirtelPO(extracted, expected) {
    const requiredFields = ['PO_Number', 'Partner', 'Buyer', 'PO_Date', 'Total_Value', 'Items'];
    return this.validateRequiredFields(extracted, expected, requiredFields);
  }

  validateHuhtamakiPO(extracted, expected) {
    const requiredFields = ['Voucher_No', 'Buyer', 'Supplier', 'Date', 'Items', 'Total_Amount'];
    return this.validateRequiredFields(extracted, expected, requiredFields);
  }

  validateRequiredFields(extracted, expected, fields) {
    const missing = [];
    fields.forEach(field => {
      if (extracted[field] === undefined) {
        missing.push(field);
      }
    });
    return missing;
  }
}

module.exports = { OptimizedParserTest };
