import { NextRequest, NextResponse } from 'next/server';
import pdf from 'pdf-parse';

// Import the comprehensive PDF parser
import { ComprehensivePDFParser } from '../../../lib/comprehensive-pdf-parser.js';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'PDF file is required' }, { status: 400 });
    }

    if (!file.name.endsWith('.pdf')) {
      return NextResponse.json({ error: 'Only PDF files are supported' }, { status: 400 });
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extract text from PDF
    const pdfData = await pdf(buffer, {
      normalizeWhitespace: true,
      disableCombineTextItems: false,
    });

    const rawText = pdfData.text;

    if (!rawText || rawText.trim().length === 0) {
      return NextResponse.json(
        {
          error: 'No text content found in PDF',
          details: 'The PDF might contain only images or be password-protected',
        },
        { status: 400 }
      );
    }

    // Parse the PDF text using comprehensive parser
    const parsedData = ComprehensivePDFParser.parseDocument(rawText);

    return NextResponse.json({
      success: true,
      fileName: file.name,
      fileSize: file.size,
      pages: pdfData.numpages,
      textLength: rawText.length,
      parsedData,
      rawText,
    });
  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      {
        error: 'Failed to parse PDF',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
