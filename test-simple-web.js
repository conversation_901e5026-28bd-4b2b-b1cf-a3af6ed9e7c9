/**
 * Simple test to verify the comprehensive parser is working
 */

const { ComprehensivePDFParser } = require('./lib/comprehensive-pdf-parser.js');
const fs = require('fs');
const pdf = require('pdf-parse');

async function testAllDocuments() {
  console.log('🧪 Testing All Document Types...\n');

  const testFiles = [
    'RSNT26T0147 - Arcsys.pdf',
    'FLCN26PO024 - Huhtamaki V2.pdf',
    'RSNT26J0018 - Resonate.pdf',
    'RSNT26J0022 - Resonate.pdf',
    'RSNT26D0127 - Ingram 32.pdf',
    'RSNT26T0129 - Ingram 29.pdf',
    'RSNT26T0122 - Diligent Solutions.pdf',
    'IAPO_66-G3474_20250718_141420.pdf',
    '2.PO_3852_10000541_0_US.pdf',
    'RSNT_SALES_INGRAM.pdf',
    'Delivery Voucher.pdf'
  ];

  let successCount = 0;
  let totalCount = testFiles.length;

  for (const fileName of testFiles) {
    try {
      console.log(`📄 Testing: ${fileName}`);
      
      const filePath = `./example file/${fileName}`;
      if (!fs.existsSync(filePath)) {
        console.log(`   ⚠️  File not found: ${fileName}`);
        continue;
      }

      const data = fs.readFileSync(filePath);
      const result = await pdf(data);
      const parsed = ComprehensivePDFParser.parseDocument(result.text);

      // Check if parsing was successful
      const hasValidData = parsed && (
        parsed.InvoiceNo || 
        parsed.PurchaseOrderNo || 
        parsed.DeliveryNoteNo || 
        parsed.DeliveryChallan ||
        parsed.DeliveryDetails ||
        parsed.Company ||
        parsed.Seller ||
        parsed.Buyer
      );

      if (hasValidData) {
        console.log(`   ✅ Successfully parsed`);
        console.log(`      Key fields: ${getKeyFields(parsed)}`);
        successCount++;
      } else {
        console.log(`   ❌ Parsing failed - no valid data extracted`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    console.log('');
  }

  console.log(`📊 Test Results:`);
  console.log(`   Total Files: ${totalCount}`);
  console.log(`   Successful: ${successCount}`);
  console.log(`   Failed: ${totalCount - successCount}`);
  console.log(`   Success Rate: ${((successCount / totalCount) * 100).toFixed(1)}%`);

  if (successCount === totalCount) {
    console.log('\n🎉 All tests passed! The PDF parser is working perfectly!');
    console.log('🌐 The Next.js application should work correctly at http://localhost:3000');
  } else {
    console.log('\n⚠️  Some tests failed. Check the parser logic.');
  }
}

function getKeyFields(parsed) {
  const fields = [];
  if (parsed.InvoiceNo) fields.push(`Invoice: ${parsed.InvoiceNo}`);
  if (parsed.PurchaseOrderNo) fields.push(`PO: ${parsed.PurchaseOrderNo}`);
  if (parsed.DeliveryNoteNo) fields.push(`DN: ${parsed.DeliveryNoteNo}`);
  if (parsed.DeliveryChallan) fields.push(`DC: ${parsed.DeliveryChallan}`);
  if (parsed.TotalAmount) fields.push(`Amount: ${parsed.TotalAmount}`);
  if (parsed.Items && parsed.Items.length) fields.push(`Items: ${parsed.Items.length}`);
  if (parsed.Goods && parsed.Goods.length) fields.push(`Goods: ${parsed.Goods.length}`);
  if (parsed.Seller) fields.push(`Seller: ${parsed.Seller.Name || 'Yes'}`);
  if (parsed.Buyer) fields.push(`Buyer: ${parsed.Buyer.Name || 'Yes'}`);
  if (parsed.Company) fields.push(`Company: ${parsed.Company.Name || 'Yes'}`);
  
  return fields.length > 0 ? fields.join(', ') : 'Basic data extracted';
}

// Run the test
testAllDocuments();
