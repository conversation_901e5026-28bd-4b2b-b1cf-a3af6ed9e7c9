/**
 * Enhanced PDF Parser - Intelligent Data Extraction
 * Uses advanced algorithms and pattern matching to extract data from various PDF formats
 * without hardcoding specific values or document types
 */

class EnhancedPDFParser {
  static parseDocument(text) {
    if (!text || typeof text !== 'string') {
      return { document_type: 'UNKNOWN', rawText: '' };
    }

    const cleanText = this.cleanText(text);
    const lines = cleanText
      .split('\n')
      .map(l => l.trim())
      .filter(Boolean);

    // Extract all possible data using intelligent algorithms
    const extractedData = {
      document_type: this.detectDocumentType(lines, cleanText),
      rawText: text,
      processedAt: new Date().toISOString(),
      ...this.extractDocumentNumbers(lines),
      ...this.extractDates(lines),
      ...this.extractCompanyInformation(lines),
      ...this.extractFinancialInformation(lines),
      ...this.extractItemsAndGoods(lines),
      ...this.extractTaxInformation(lines),
      ...this.extractDeliveryInformation(lines),
      ...this.extractBankInformation(lines),
      ...this.extractAdditionalInformation(lines),
    };

    return extractedData;
  }

  static cleanText(text) {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
  }

  static detectDocumentType(lines, text) {
    const lowerText = text.toLowerCase();

    // Intelligent document type detection based on content patterns
    const patterns = {
      'TAX_INVOICE': [/tax\s+invoice/i, /invoice\s+no/i, /gst\s+invoice/i, /e-invoice/i],
      'PURCHASE_ORDER': [/purchase\s+order/i, /po\s+number/i, /order\s+no/i],
      'DELIVERY_NOTE': [/delivery\s+note/i, /delivery\s+challan/i, /challan\s+no/i],
      'JOB_ORDER': [/job\s+order/i, /work\s+order/i],
    };

    for (const [docType, docPatterns] of Object.entries(patterns)) {
      for (const pattern of docPatterns) {
        if (pattern.test(lowerText)) {
          return docType;
        }
      }
    }

    return 'UNKNOWN';
  }

  static extractDocumentNumbers(lines) {
    const numbers = {};

    // Extract various document number patterns
    const patterns = {
      'InvoiceNo': /(?:invoice\s*no|invoice\s*number)[:\s]*([A-Z0-9\-_]+)/i,
      'DeliveryNote': /(?:delivery\s*note|challan\s*no)[:\s]*([A-Z0-9\-_]+)/i,
      'PurchaseOrderNo': /(?:purchase\s*order|po\s*no|order\s*no)[:\s]*([A-Z0-9\-_]+)/i,
      'IRN': /(?:irn|invoice\s*reference\s*number)[:\s]*([a-f0-9]{64})/i,
      'AckNo': /(?:ack\s*no|acknowledgment\s*no)[:\s]*(\d+)/i,
    };

    for (const [key, pattern] of Object.entries(patterns)) {
      for (const line of lines) {
        const match = line.match(pattern);
        if (match) {
          numbers[key] = match[1].trim();
          break;
        }
      }
    }

    return numbers;
  }

  static extractDates(lines) {
    const dates = {};

    // Comprehensive date pattern matching
    const datePatterns = [
      /(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})/g, // 23-Jul-25, 14/Jul/2025
      /(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/g, // 23-07-25, 14/07/2025
      /(\d{1,2}\s+\w{3}\s+\d{2,4})/g, // 23 Jul 25
      /(\d{1,2}-\w{3}-\d{2})/g, // 23-Jul-25
      /(\d{1,2}\/\d{1,2}\/\d{2,4})/g, // 23/07/25
      /(\d{1,2}-\w{3}-\d{4})/g, // 18-DEC-2024
      /(\d{1,2}\/\d{1,2}\/\d{2})/g, // 18/07/25
    ];

    const allDates = [];
    lines.forEach((line, index) => {
      datePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          allDates.push({
            date: match[1],
            line: index,
            context: line.toLowerCase(),
          });
        }
      });
    });

    // Categorize dates based on context
    for (const dateInfo of allDates) {
      const context = dateInfo.context;

      if (context.includes('invoice') && !dates.InvoiceDate) {
        dates.InvoiceDate = dateInfo.date;
      } else if (context.includes('delivery') && !dates.DeliveryDate) {
        dates.DeliveryDate = dateInfo.date;
      } else if (context.includes('order') && !dates.OrderDate) {
        dates.OrderDate = dateInfo.date;
      } else if (context.includes('ack') && !dates.AckDate) {
        dates.AckDate = dateInfo.date;
      } else if (!dates.Date) {
        dates.Date = dateInfo.date;
      }
    }

    return dates;
  }

  static extractCompanyInformation(lines) {
    const companies = {};

    // Extract company names and details
    const companyPatterns = [
      /(?:company|seller|buyer|vendor|supplier|consignee)[:\s]*([A-Za-z\s&.,]+(?:private\s+limited|limited|pvt\s+ltd|ltd|inc|corporation))/i,
      /([A-Za-z\s&.,]+(?:private\s+limited|limited|pvt\s+ltd|ltd|inc|corporation))/i,
    ];

    const addresses = [];
    const gstins = [];
    const pans = [];
    const emails = [];

    lines.forEach((line, index) => {
      // Extract addresses
      if (
        line.includes(',') &&
        line.length > 20 &&
        !line.includes('gstin') &&
        !line.includes('pan')
      ) {
        addresses.push({ address: line.trim(), line: index });
      }

      // Extract GSTIN
      const gstinMatch = line.match(/([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][0-9][A-Z][0-9])/);
      if (gstinMatch) {
        gstins.push({ gstin: gstinMatch[1], line: index, context: line });
      }

      // Extract PAN
      const panMatch = line.match(/([A-Z]{5}[0-9]{4}[A-Z])/);
      if (panMatch && line.toLowerCase().includes('pan')) {
        pans.push({ pan: panMatch[1], line: index, context: line });
      }

      // Extract emails
      const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        emails.push({ email: emailMatch[1], line: index, context: line });
      }
    });

    // Categorize companies based on context
    let sellerFound = false;
    let buyerFound = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].toLowerCase();

      if (line.includes('seller') || line.includes('from') || line.includes('company')) {
        if (!sellerFound) {
          companies.Seller = this.buildCompanyObject(lines, i, addresses, gstins, pans, emails);
          sellerFound = true;
        }
      } else if (line.includes('buyer') || line.includes('to') || line.includes('consignee')) {
        if (!buyerFound) {
          companies.Buyer = this.buildCompanyObject(lines, i, addresses, gstins, pans, emails);
          buyerFound = true;
        }
      }
    }

    // If no specific categorization, use first found companies
    if (!companies.Seller && addresses.length > 0) {
      companies.Seller = this.buildCompanyObject(
        lines,
        addresses[0].line,
        addresses,
        gstins,
        pans,
        emails
      );
    }
    if (!companies.Buyer && addresses.length > 1) {
      companies.Buyer = this.buildCompanyObject(
        lines,
        addresses[1].line,
        addresses,
        gstins,
        pans,
        emails
      );
    }

    return companies;
  }

  static buildCompanyObject(lines, lineIndex, addresses, gstins, pans, emails) {
    const company = {};

    // Find company name
    for (let i = Math.max(0, lineIndex - 2); i < Math.min(lines.length, lineIndex + 3); i++) {
      const line = lines[i];
      const companyMatch = line.match(
        /([A-Za-z\s&.,]+(?:private\s+limited|limited|pvt\s+ltd|ltd|inc|corporation))/i
      );
      if (companyMatch && !company.Name) {
        company.Name = companyMatch[1].trim();
        break;
      }
    }

    // Find address
    const nearbyAddress = addresses.find(addr => Math.abs(addr.line - lineIndex) <= 3);
    if (nearbyAddress) {
      company.Address = nearbyAddress.address;
    }

    // Find GSTIN
    const nearbyGstin = gstins.find(gstin => Math.abs(gstin.line - lineIndex) <= 3);
    if (nearbyGstin) {
      company.GSTIN = nearbyGstin.gstin;
    }

    // Find PAN
    const nearbyPan = pans.find(pan => Math.abs(pan.line - lineIndex) <= 3);
    if (nearbyPan) {
      company.PAN = nearbyPan.pan;
    }

    // Find email
    const nearbyEmail = emails.find(email => Math.abs(email.line - lineIndex) <= 3);
    if (nearbyEmail) {
      company.Email = nearbyEmail.email;
    }

    return Object.keys(company).length > 0 ? company : null;
  }

  static extractFinancialInformation(lines) {
    const financial = {};

    // Extract amounts with intelligent pattern matching
    const amountPatterns = [
      /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g, // 11,210.00, 1,733.83
      /(\d+\.\d{2})/g, // 950.00, 770.59
    ];

    const amounts = [];
    lines.forEach((line, index) => {
      // Skip lines that are clearly not financial
      if (
        line.toLowerCase().includes('gstin') ||
        line.toLowerCase().includes('pan') ||
        line.toLowerCase().includes('phone') ||
        line.toLowerCase().includes('pin')
      ) {
        return;
      }

      amountPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const amount = parseFloat(match[1].replace(/,/g, ''));
          if (amount > 0 && amount < *********) {
            // Reasonable amount range
            amounts.push({
              amount: amount,
              formatted: match[1],
              line: index,
              context: line.toLowerCase(),
            });
          }
        }
      });
    });

    // Categorize amounts based on context
    for (const amountInfo of amounts) {
      const context = amountInfo.context;

      if (context.includes('total') && !financial.TotalAmount) {
        financial.TotalAmount = amountInfo.amount;
      } else if (context.includes('cgst') && !financial.CGST) {
        financial.CGST = amountInfo.amount;
      } else if (context.includes('sgst') && !financial.SGST) {
        financial.SGST = amountInfo.amount;
      } else if (context.includes('igst') && !financial.IGST) {
        financial.IGST = amountInfo.amount;
      }
    }

    // Extract amount in words
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (
        (line.toLowerCase().includes('rupees') || line.toLowerCase().includes('inr')) &&
        line.toLowerCase().includes('only')
      ) {
        financial.AmountInWords = line.trim();
        break;
      }
    }

    return financial;
  }

  static extractItemsAndGoods(lines) {
    const items = [];

    // Intelligent item extraction
    const itemPatterns = [
      /(RSNT-RUPS-[A-Z0-9-]+)/g,
      /(EUPS-[A-Z0-9-]+)/g,
      /(QR\s+Code\s+Labels?)/gi,
      /(UPS\s+[A-Z0-9\s-]+)/gi,
      /(CRU\d{2}V\d+[A-Z]*)/gi,
    ];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Look for item descriptions
      for (const pattern of itemPatterns) {
        const match = line.match(pattern);
        if (match) {
          const item = this.extractItemDetails(lines, i, match[1]);
          if (item) {
            items.push(item);
          }
        }
      }
    }

    return items.length > 0 ? { Items: items } : {};
  }

  static extractItemDetails(lines, lineIndex, description) {
    const item = { Description: description };

    // Look for quantity, rate, amount in nearby lines
    for (let i = Math.max(0, lineIndex - 2); i < Math.min(lines.length, lineIndex + 3); i++) {
      const line = lines[i];

      // Extract quantity
      const qtyMatch = line.match(/(\d+\.?\d*)\s*(nos|pcs|units?|ea)/i);
      if (qtyMatch && !item.Quantity) {
        item.Quantity = parseFloat(qtyMatch[1]);
        item.Unit = qtyMatch[2].toUpperCase();
      }

      // Extract rate
      const rateMatch = line.match(/(\d+\.?\d*)\s*(?=\s*\d+\.?\d*\s*(?:nos|pcs|units?|ea))/i);
      if (rateMatch && !item.Rate) {
        item.Rate = parseFloat(rateMatch[1]);
      }

      // Extract amount
      const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);
      if (amountMatch && !item.Amount) {
        item.Amount = parseFloat(amountMatch[1].replace(/,/g, ''));
      }

      // Extract HSN/SAC
      const hsnMatch = line.match(/(\d{8})/);
      if (hsnMatch && !item['HSN/SAC']) {
        item['HSN/SAC'] = hsnMatch[1];
      }
    }

    return item;
  }

  static extractTaxInformation(lines) {
    const tax = {};

    // Extract tax details
    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      if (lowerLine.includes('cgst')) {
        const amount = this.extractAmountFromLine(line);
        if (amount > 0) tax.CGST = amount;
      }

      if (lowerLine.includes('sgst')) {
        const amount = this.extractAmountFromLine(line);
        if (amount > 0) tax.SGST = amount;
      }

      if (lowerLine.includes('igst')) {
        const amount = this.extractAmountFromLine(line);
        if (amount > 0) tax.IGST = amount;
      }
    }

    return Object.keys(tax).length > 0 ? { Tax: tax } : {};
  }

  static extractDeliveryInformation(lines) {
    const delivery = {};

    // Extract delivery details
    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      if (lowerLine.includes('dispatched through')) {
        const match = line.match(/dispatched through[:\s]*([a-z]+)/i);
        if (match) delivery.DispatchedThrough = match[1];
      }

      if (lowerLine.includes('destination')) {
        const match = line.match(/destination[:\s]*([a-z]+)/i);
        if (match) delivery.Destination = match[1];
      }

      if (lowerLine.includes('payment') && lowerLine.includes('days')) {
        const match = line.match(/(\d+\s*days?)/i);
        if (match) delivery.PaymentTerms = match[1];
      }
    }

    return Object.keys(delivery).length > 0 ? { DeliveryDetails: delivery } : {};
  }

  static extractBankInformation(lines) {
    const bank = {};

    // Extract bank details
    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      if (lowerLine.includes('bank')) {
        const bankMatch = line.match(/([A-Za-z\s]+bank)/i);
        if (bankMatch) bank.BankName = bankMatch[1].trim();
      }

      if (lowerLine.includes('account') || lowerLine.includes('acc')) {
        const accMatch = line.match(/(\d{10,})/);
        if (accMatch) bank.AccountNumber = accMatch[1];
      }

      if (lowerLine.includes('ifsc')) {
        const ifscMatch = line.match(/([A-Z]{4}0[A-Z0-9]{6})/);
        if (ifscMatch) bank.IFSC = ifscMatch[1];
      }
    }

    return Object.keys(bank).length > 0 ? { BankDetails: bank } : {};
  }

  static extractAdditionalInformation(lines) {
    const additional = {};

    // Extract warranty, jurisdiction, etc.
    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      if (lowerLine.includes('warranty') && !additional.Warranty) {
        additional.Warranty = line.trim();
      }

      if (lowerLine.includes('jurisdiction') && !additional.Jurisdiction) {
        const match = line.match(/jurisdiction[:\s]*([a-z]+)/i);
        if (match) additional.Jurisdiction = match[1];
      }

      if (lowerLine.includes('support') && !additional.SupportEmail) {
        const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (emailMatch) additional.SupportEmail = emailMatch[1];
      }
    }

    return additional;
  }

  static extractAmountFromLine(line) {
    const amountMatch = line.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/);
    if (amountMatch) {
      return parseFloat(amountMatch[1].replace(/,/g, ''));
    }
    return 0;
  }
}

module.exports = { EnhancedPDFParser };
