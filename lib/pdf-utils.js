/**
 * PDF Parsing Utilities
 * Reusable functions for PDF text processing and data extraction
 * Handles both vertical and horizontal text orders
 */

class PDFUtils {
  /**
   * Clean and normalize PDF text
   * Handles various text encodings and formats
   */
  static cleanText(text) {
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .replace(/[^\x20-\x7E\n]/g, ' ')
      .replace(/\u00A0/g, ' ') // Non-breaking space
      .replace(/\u2013|\u2014/g, '-') // En dash, Em dash
      .trim();
  }

  /**
   * Extract text in both vertical and horizontal orders
   * Useful for PDFs with complex layouts
   */
  static extractTextBothOrders(lines) {
    const horizontal = lines.join(' ');
    const vertical = lines.map((line, index) => {
      const words = line.split(/\s+/);
      return words.map((word, wordIndex) => ({
        word,
        line: index,
        position: wordIndex,
        context: line
      }));
    }).flat();

    return { horizontal, vertical };
  }

  /**
   * Smart date extraction with multiple format support
   */
  static extractDates(text) {
    const datePatterns = [
      /(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})/gi,  // 18-DEC-24, 25-Jul-25
      /(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/gi, // 18/07/25, 26/07/25
      /(\d{1,2}\s+\w{3}\s+\d{2,4})/gi,      // 3 Jul 25
      /(\w{3}\s+\d{1,2},?\s+\d{4})/gi       // Jul 25, 2025
    ];

    const dates = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      datePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          dates.push({
            original: match[1],
            normalized: this.normalizeDate(match[1]),
            line: lineIndex,
            context: line.trim(),
            confidence: this.calculateDateConfidence(match[1], line)
          });
        }
      });
    });

    return dates.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Normalize date to consistent format
   */
  static normalizeDate(dateStr) {
    const monthMap = {
      'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
      'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
      'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
    };

    // Handle DD-MMM-YY format
    const match = dateStr.match(/(\d{1,2})[-\/](\w{3})[-\/](\d{2,4})/i);
    if (match) {
      const day = match[1].padStart(2, '0');
      const month = monthMap[match[2].toLowerCase()] || match[2];
      const year = match[3].length === 2 ? '20' + match[3] : match[3];
      return `${day}-${month}-${year}`;
    }

    return dateStr;
  }

  /**
   * Calculate confidence score for date extraction
   */
  static calculateDateConfidence(dateStr, context) {
    let confidence = 0.5;
    
    // Higher confidence for dates near keywords
    const dateKeywords = ['date', 'dated', 'invoice', 'delivery', 'po'];
    const lowerContext = context.toLowerCase();
    
    dateKeywords.forEach(keyword => {
      if (lowerContext.includes(keyword)) {
        confidence += 0.2;
      }
    });

    // Higher confidence for proper date formats
    if (/\d{1,2}[-\/]\w{3}[-\/]\d{2,4}/.test(dateStr)) {
      confidence += 0.3;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Smart number extraction with classification
   */
  static extractNumbers(text) {
    const numberPatterns = [
      { pattern: /(RSNT\d{2}[A-Z]\d+)/g, type: 'RESONATE_DOC' },
      { pattern: /(FLCN\d{2}PO\d+)/g, type: 'FALCONN_PO' },
      { pattern: /(BAL-[A-Z-]+\/[A-Z]+\/\d+)/g, type: 'AIRTEL_PO' },
      { pattern: /(\d{2}-[A-Z]\d+)/g, type: 'INGRAM_REF' },
      { pattern: /(\d{15})/g, type: 'IRN' },
      { pattern: /(\d{12})/g, type: 'ACK_NO' }
    ];

    const numbers = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      numberPatterns.forEach(({ pattern, type }) => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          numbers.push({
            value: match[1],
            type,
            line: lineIndex,
            context: line.trim(),
            confidence: this.calculateNumberConfidence(match[1], line, type)
          });
        }
      });
    });

    return numbers.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Calculate confidence for number extraction
   */
  static calculateNumberConfidence(number, context, type) {
    let confidence = 0.5;
    
    const contextKeywords = {
      'RESONATE_DOC': ['invoice', 'delivery', 'job'],
      'FALCONN_PO': ['purchase', 'order', 'po'],
      'AIRTEL_PO': ['purchase', 'order', 'bal'],
      'INGRAM_REF': ['reference', 'ref', 'order'],
      'IRN': ['irn', 'invoice'],
      'ACK_NO': ['ack', 'acknowledgment']
    };

    const keywords = contextKeywords[type] || [];
    const lowerContext = context.toLowerCase();
    
    keywords.forEach(keyword => {
      if (lowerContext.includes(keyword)) {
        confidence += 0.2;
      }
    });

    return Math.min(confidence, 1.0);
  }

  /**
   * Smart amount extraction with context awareness
   */
  static extractAmounts(text) {
    const amountPatterns = [
      /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g,  // 11,210.00, 1,733.83
      /(\d+\.\d{2})/g                        // 950.00, 770.59
    ];

    const amounts = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      amountPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const value = parseFloat(match[1].replace(/,/g, ''));
          if (value > 0) {
            amounts.push({
              value,
              original: match[1],
              line: lineIndex,
              context: line.trim(),
              type: this.classifyAmount(value, line),
              confidence: this.calculateAmountConfidence(value, line)
            });
          }
        }
      });
    });

    return amounts.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Classify amount based on value and context
   */
  static classifyAmount(value, context) {
    const lowerContext = context.toLowerCase();
    
    if (lowerContext.includes('total') && value > 1000) return 'TOTAL';
    if (lowerContext.includes('gst') || lowerContext.includes('tax')) return 'TAX';
    if (lowerContext.includes('rate') && value < 10000) return 'RATE';
    if (value > 50000) return 'GRAND_TOTAL';
    if (value > 10000) return 'SUBTOTAL';
    if (value < 100) return 'RATE';
    
    return 'AMOUNT';
  }

  /**
   * Calculate confidence for amount extraction
   */
  static calculateAmountConfidence(value, context) {
    let confidence = 0.5;
    
    const amountKeywords = ['total', 'amount', 'rate', 'price', 'cost', 'gst', 'tax'];
    const lowerContext = context.toLowerCase();
    
    amountKeywords.forEach(keyword => {
      if (lowerContext.includes(keyword)) {
        confidence += 0.2;
      }
    });

    // Higher confidence for properly formatted amounts
    if (value % 1 === 0 || (value * 100) % 1 === 0) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Extract company information with GSTIN and PAN
   */
  static extractCompanies(text) {
    const companyPatterns = [
      /bharti airtel limited/gi,
      /resonate systems private limited/gi,
      /huhtamaki india limited/gi,
      /falconn esdm private limited/gi,
      /ingram micro india private limited/gi,
      /diligent solutions/gi,
      /arcsys techsolutions private limited/gi
    ];

    const companies = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      companyPatterns.forEach(pattern => {
        const match = line.match(pattern);
        if (match) {
          const company = {
            name: match[0],
            line: lineIndex,
            context: line.trim(),
            gstin: this.findNearbyGSTIN(lines, lineIndex),
            pan: this.findNearbyPAN(lines, lineIndex),
            address: this.findNearbyAddress(lines, lineIndex)
          };
          companies.push(company);
        }
      });
    });

    return companies;
  }

  /**
   * Find GSTIN near a company mention
   */
  static findNearbyGSTIN(lines, companyLineIndex) {
    const gstinPattern = /(\d{2}[A-Z]{5}\d{4}[A-Z]\d[A-Z]\d)/;
    
    // Search in nearby lines (±3 lines)
    for (let i = Math.max(0, companyLineIndex - 3); i <= Math.min(lines.length - 1, companyLineIndex + 3); i++) {
      const match = lines[i].match(gstinPattern);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * Find PAN near a company mention
   */
  static findNearbyPAN(lines, companyLineIndex) {
    const panPattern = /([A-Z]{5}\d{4}[A-Z])/;
    
    // Search in nearby lines (±3 lines)
    for (let i = Math.max(0, companyLineIndex - 3); i <= Math.min(lines.length - 1, companyLineIndex + 3); i++) {
      const match = lines[i].match(panPattern);
      if (match) {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * Find address near a company mention
   */
  static findNearbyAddress(lines, companyLineIndex) {
    const addressIndicators = /floor|flat|plot|building|road|street|bangalore|delhi|mumbai|chennai|cochin/i;
    
    // Search in nearby lines (±5 lines)
    for (let i = Math.max(0, companyLineIndex - 2); i <= Math.min(lines.length - 1, companyLineIndex + 5); i++) {
      if (addressIndicators.test(lines[i]) && lines[i].length > 20) {
        return lines[i].trim();
      }
    }
    
    return null;
  }

  /**
   * Extract HSN codes with validation
   */
  static extractHSNs(text) {
    const hsnPattern = /(\d{4}\.?\d{2}\.?\d{2}|\d{8})/g;
    const hsns = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      if (line.toLowerCase().includes('hsn') || line.toLowerCase().includes('sac')) {
        let match;
        while ((match = hsnPattern.exec(line)) !== null) {
          hsns.push({
            value: match[1].replace(/\./g, ''),
            original: match[1],
            line: lineIndex,
            context: line.trim(),
            valid: this.validateHSN(match[1])
          });
        }
      }
    });

    return hsns.filter(hsn => hsn.valid);
  }

  /**
   * Validate HSN code format
   */
  static validateHSN(hsn) {
    const cleanHSN = hsn.replace(/\./g, '');
    return /^\d{8}$/.test(cleanHSN) || /^\d{4}$/.test(cleanHSN) || /^\d{6}$/.test(cleanHSN);
  }

  /**
   * Extract items with intelligent pattern matching
   */
  static extractItems(text) {
    const itemPatterns = [
      /RSNT-RUPS-[A-Z0-9-]+/gi,
      /EUPS-[A-Z0-9-]+/gi,
      /QR Code Label[s]?[^a-z]*/gi,
      /Power Supply Adaptor[^a-z]*/gi
    ];

    const items = [];
    const lines = text.split('\n');

    lines.forEach((line, lineIndex) => {
      itemPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          items.push({
            description: match[0].trim(),
            line: lineIndex,
            context: line.trim(),
            category: this.categorizeItem(match[0])
          });
        }
      });
    });

    return items;
  }

  /**
   * Categorize items based on description
   */
  static categorizeItem(description) {
    const desc = description.toLowerCase();
    
    if (desc.includes('rsnt-rups')) return 'ROUTER_UPS';
    if (desc.includes('eups')) return 'EDGE_UPS';
    if (desc.includes('qr code')) return 'QR_LABEL';
    if (desc.includes('power supply')) return 'POWER_ADAPTOR';
    
    return 'OTHER';
  }

  /**
   * Format amount with currency
   */
  static formatAmount(amount, currency = 'INR') {
    if (typeof amount === 'string') return amount;
    return `${amount.toFixed(2)} ${currency}`;
  }

  /**
   * Validate extracted data completeness
   */
  static validateExtractedData(data, requiredFields) {
    const missing = [];
    const warnings = [];

    requiredFields.forEach(field => {
      if (!data[field] || data[field] === '' || data[field] === 'MISSING') {
        missing.push(field);
      }
    });

    // Check for suspicious values
    if (data.Items && Array.isArray(data.Items) && data.Items.length === 0) {
      warnings.push('No items extracted');
    }

    if (data.Total_Amount && parseFloat(data.Total_Amount) === 0) {
      warnings.push('Total amount is zero');
    }

    return { missing, warnings, isValid: missing.length === 0 };
  }
}

module.exports = { PDFUtils };
