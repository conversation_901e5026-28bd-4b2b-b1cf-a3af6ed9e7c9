/**
 * Optimized PDF Parser - Single Unified Parser for All Document Types
 * Handles 11 document types with dynamic logic and no hardcoded values
 * Supports both vertical and horizontal text orders
 */

class OptimizedPDFParser {
  static parseDocument(text) {
    if (!text || typeof text !== 'string') {
      return { document_type: 'UNKNOWN', rawText: '' };
    }

    const cleanText = this.cleanText(text);
    const lines = cleanText.split('\n').map(l => l.trim()).filter(Boolean);
    const docType = this.detectDocumentType(lines, cleanText);

    // Create comprehensive context with all extracted data
    const context = {
      lines,
      text: cleanText,
      docType,
      companies: this.extractCompanies(lines),
      dates: this.extractDates(lines),
      numbers: this.extractNumbers(lines),
      amounts: this.extractAmounts(lines),
      items: this.extractItems(lines),
      addresses: this.extractAddresses(lines),
      gstins: this.extractGSTINs(lines),
      pans: this.extractPANs(lines),
      emails: this.extractEmails(lines),
      hsns: this.extractHSNs(lines)
    };

    // Route to appropriate parser based on document type
    return this.routeToParser(context);
  }

  static cleanText(text) {
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .replace(/[^\x20-\x7E\n]/g, ' ')
      .trim();
  }

  static detectDocumentType(lines, text) {
    const lowerText = text.toLowerCase();
    
    // Airtel PO Detection
    if (lowerText.includes('bharti airtel limited') && 
        lowerText.includes('resonate systems') && 
        lowerText.includes('purchase order')) {
      return 'AIRTEL_PO';
    }
    
    // Huhtamaki PO Detection
    if (lowerText.includes('huhtamaki india limited') && 
        lowerText.includes('falconn esdm') && 
        lowerText.includes('purchase order')) {
      return 'HUHTAMAKI_PO';
    }
    
    // Ingram PO Detection
    if (lowerText.includes('ingram micro india private limited') && 
        lowerText.includes('resonate systems') && 
        lowerText.includes('purchase order')) {
      return 'INGRAM_PO';
    }
    
    // Delivery Voucher Detection
    if (lowerText.includes('delivery voucher') || 
        (lowerText.includes('delivery note') && lowerText.includes('rsnt26d03'))) {
      return 'DELIVERY_VOUCHER';
    }
    
    // Resonate Job Order Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('falconn esdm') && 
        lowerText.includes('job order')) {
      return 'RESONATE_JOB_ORDER';
    }
    
    // Resonate Delivery Note Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('falconn esdm') && 
        lowerText.includes('delivery note') && 
        !lowerText.includes('job order')) {
      return 'RESONATE_DELIVERY';
    }
    
    // Ingram Delivery Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('ingram micro india private limited') && 
        lowerText.includes('delivery challan')) {
      return 'INGRAM_DELIVERY';
    }
    
    // Sales Invoice Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('ingram micro india private limited') && 
        lowerText.includes('tax invoice') && 
        !lowerText.includes('delivery challan')) {
      return 'SALES_INVOICE';
    }
    
    // Diligent Invoice Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('diligent solutions') && 
        lowerText.includes('tax invoice')) {
      return 'DILIGENT_INVOICE';
    }
    
    // Arcsys Invoice Detection
    if (lowerText.includes('resonate systems') && 
        lowerText.includes('arcsys techsolutions') && 
        lowerText.includes('tax invoice')) {
      return 'ARCSYS_INVOICE';
    }

    return 'UNKNOWN';
  }

  static routeToParser(context) {
    switch (context.docType) {
      case 'AIRTEL_PO':
        return this.parseAirtelPO(context);
      case 'HUHTAMAKI_PO':
        return this.parseHuhtamakiPO(context);
      case 'INGRAM_PO':
        return this.parseIngramPO(context);
      case 'DELIVERY_VOUCHER':
        return this.parseDeliveryVoucher(context);
      case 'RESONATE_JOB_ORDER':
        return this.parseResonateJobOrder(context);
      case 'RESONATE_DELIVERY':
        return this.parseResonateDelivery(context);
      case 'INGRAM_DELIVERY':
        return this.parseIngramDelivery(context);
      case 'SALES_INVOICE':
        return this.parseSalesInvoice(context);
      case 'DILIGENT_INVOICE':
        return this.parseDiligentInvoice(context);
      case 'ARCSYS_INVOICE':
        return this.parseArcsysInvoice(context);
      default:
        return { document_type: 'UNKNOWN', rawText: context.text };
    }
  }

  // Extraction utility methods
  static extractCompanies(lines) {
    const companies = [];
    const companyPatterns = [
      /bharti airtel limited/i,
      /resonate systems private limited/i,
      /huhtamaki india limited/i,
      /falconn esdm private limited/i,
      /ingram micro india private limited/i,
      /diligent solutions/i,
      /arcsys techsolutions private limited/i
    ];

    lines.forEach((line, index) => {
      companyPatterns.forEach(pattern => {
        if (pattern.test(line)) {
          companies.push({
            name: line.match(pattern)[0],
            line: index,
            context: lines.slice(Math.max(0, index - 2), index + 3).join(' ')
          });
        }
      });
    });

    return companies;
  }

  static extractDates(lines) {
    const dates = [];
    const datePatterns = [
      /(\d{1,2}[-\/]\w{3}[-\/]\d{2,4})/g,  // 18-DEC-24, 25-Jul-25
      /(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/g, // 18/07/25, 26/07/25
      /(\d{1,2}\s+\w{3}\s+\d{2,4})/g       // 3 Jul 25
    ];

    lines.forEach((line, index) => {
      datePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          dates.push({
            original: match[1],
            line: index,
            context: line
          });
        }
      });
    });

    return dates;
  }

  static extractNumbers(lines) {
    const numbers = [];
    const numberPatterns = [
      /(RSNT\d{2}[A-Z]\d+)/g,     // RSNT26T0147, RSNT26D0127
      /(FLCN\d{2}PO\d+)/g,        // FLCN26PO024
      /(BAL-[A-Z-]+\/\d+)/g,      // BAL-EGB-ISP--J&K/PUR/10000541
      /(\d{2}-[A-Z]\d+)/g,        // 66-G3474, 38-F7554
      /(\d{15})/g                 // IRN numbers
    ];

    lines.forEach((line, index) => {
      numberPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          numbers.push({
            value: match[1],
            line: index,
            context: line,
            type: this.classifyNumber(match[1])
          });
        }
      });
    });

    return numbers;
  }

  static classifyNumber(number) {
    if (number.includes('RSNT') && number.includes('T')) return 'INVOICE_NO';
    if (number.includes('RSNT') && number.includes('D')) return 'DELIVERY_NO';
    if (number.includes('RSNT') && number.includes('J')) return 'JOB_ORDER_NO';
    if (number.includes('FLCN')) return 'FALCONN_PO';
    if (number.includes('BAL-')) return 'AIRTEL_PO';
    if (/^\d{2}-[A-Z]\d+$/.test(number)) return 'INGRAM_REF';
    if (/^\d{15}$/.test(number)) return 'IRN';
    return 'GENERAL';
  }

  static extractAmounts(lines) {
    const amounts = [];
    const amountPatterns = [
      /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g,  // 11,210.00, 1,733.83
      /(\d+\.\d{2})/g                        // 950.00, 770.59
    ];

    lines.forEach((line, index) => {
      amountPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          const value = parseFloat(match[1].replace(/,/g, ''));
          if (value > 0) {
            amounts.push({
              value,
              original: match[1],
              line: index,
              context: line
            });
          }
        }
      });
    });

    return amounts.sort((a, b) => b.value - a.value); // Sort by value descending
  }

  static extractItems(lines) {
    const items = [];
    const itemPatterns = [
      /RSNT-RUPS-[A-Z0-9-]+/gi,
      /EUPS-[A-Z0-9-]+/gi,
      /QR Code Label[s]?/gi,
      /Power Supply Adaptor/gi
    ];

    lines.forEach((line, index) => {
      itemPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(line)) !== null) {
          items.push({
            description: match[0],
            line: index,
            context: line
          });
        }
      });
    });

    return items;
  }

  static extractAddresses(lines) {
    const addresses = [];
    // Look for lines that contain address indicators
    const addressIndicators = [
      /floor|flat|plot|building|road|street|bangalore|delhi|mumbai|chennai|cochin/i
    ];

    lines.forEach((line, index) => {
      addressIndicators.forEach(pattern => {
        if (pattern.test(line) && line.length > 20) {
          addresses.push({
            text: line,
            line: index,
            context: lines.slice(Math.max(0, index - 1), index + 2).join(' ')
          });
        }
      });
    });

    return addresses;
  }

  static extractGSTINs(lines) {
    const gstins = [];
    const gstinPattern = /(\d{2}[A-Z]{5}\d{4}[A-Z]\d[A-Z]\d)/g;

    lines.forEach((line, index) => {
      let match;
      while ((match = gstinPattern.exec(line)) !== null) {
        gstins.push({
          value: match[1],
          line: index,
          context: line
        });
      }
    });

    return gstins;
  }

  static extractPANs(lines) {
    const pans = [];
    const panPattern = /([A-Z]{5}\d{4}[A-Z])/g;

    lines.forEach((line, index) => {
      let match;
      while ((match = panPattern.exec(line)) !== null) {
        pans.push({
          value: match[1],
          line: index,
          context: line
        });
      }
    });

    return pans;
  }

  static extractEmails(lines) {
    const emails = [];
    const emailPattern = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;

    lines.forEach((line, index) => {
      let match;
      while ((match = emailPattern.exec(line)) !== null) {
        emails.push({
          value: match[1],
          line: index,
          context: line
        });
      }
    });

    return emails;
  }

  static extractHSNs(lines) {
    const hsns = [];
    const hsnPattern = /(\d{4}\.?\d{2}\.?\d{2}|\d{8})/g;

    lines.forEach((line, index) => {
      if (line.toLowerCase().includes('hsn') || line.toLowerCase().includes('sac')) {
        let match;
        while ((match = hsnPattern.exec(line)) !== null) {
          hsns.push({
            value: match[1],
            line: index,
            context: line
          });
        }
      }
    });

    return hsns;
  }
