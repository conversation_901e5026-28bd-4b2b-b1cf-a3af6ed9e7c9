/**
 * Test script to verify the web API is working correctly
 */

const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testWebAPI() {
  try {
    console.log('🧪 Testing Web API...\n');

    // Test with Arcsys Invoice
    const pdfPath = './example file/RSNT26T0147 - Arcsys.pdf';
    const pdfBuffer = fs.readFileSync(pdfPath);
    
    const formData = new FormData();
    formData.append('file', pdfBuffer, {
      filename: 'RSNT26T0147 - Arcsys.pdf',
      contentType: 'application/pdf'
    });

    console.log('📤 Sending PDF to API...');
    const response = await fetch('http://localhost:3000/api/parse-pdf', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    console.log('✅ API Response received successfully!');
    console.log('📊 Response Summary:');
    console.log(`   Success: ${result.success}`);
    console.log(`   File Name: ${result.fileName}`);
    console.log(`   File Size: ${result.fileSize} bytes`);
    console.log(`   Pages: ${result.pages}`);
    console.log(`   Text Length: ${result.textLength} characters`);
    
    console.log('\n📋 Parsed Data Summary:');
    const parsed = result.parsedData;
    console.log(`   Invoice No: ${parsed.InvoiceNo || 'N/A'}`);
    console.log(`   Invoice Date: ${parsed.InvoiceDate || 'N/A'}`);
    console.log(`   Total Amount: ${parsed.TotalAmount || 'N/A'}`);
    console.log(`   Seller: ${parsed.Seller ? parsed.Seller.Name : 'N/A'}`);
    console.log(`   Buyer: ${parsed.Buyer ? parsed.Buyer.Name : 'N/A'}`);
    console.log(`   Items Count: ${parsed.Items ? parsed.Items.length : 0}`);
    
    if (parsed.Items && parsed.Items.length > 0) {
      console.log('\n📦 First Item Details:');
      const firstItem = parsed.Items[0];
      console.log(`   Description: ${firstItem.Description ? firstItem.Description.substring(0, 50) + '...' : 'N/A'}`);
      console.log(`   Quantity: ${firstItem.Quantity || 'N/A'}`);
      console.log(`   Rate: ${firstItem.Rate || 'N/A'}`);
      console.log(`   Amount: ${firstItem.Amount || 'N/A'}`);
    }

    console.log('\n🎉 Web API test completed successfully!');
    console.log('🌐 The Next.js application is working correctly at http://localhost:3000');
    
  } catch (error) {
    console.error('❌ Web API test failed:', error.message);
    console.log('\n💡 Make sure the Next.js server is running with: npm run dev');
  }
}

// Run the test
testWebAPI();
