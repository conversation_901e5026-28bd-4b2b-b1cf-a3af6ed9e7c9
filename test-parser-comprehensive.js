const fs = require('fs');
const path = require('path');
const pdf = require('pdf-parse');
const { ComprehensivePDFParser } = require('./lib/comprehensive-pdf-parser.js');

// Test configuration
const EXAMPLE_FOLDER = './example file';
const EXPECTED_JSON_FILE = path.join(EXAMPLE_FOLDER, 'pdf_Expected_json_output.json');

// Load expected JSON output
function loadExpectedOutput() {
  try {
    const content = fs.readFileSync(EXPECTED_JSON_FILE, 'utf8');
    const expectedOutputs = {};

    // Parse as JSON array
    const jsonArray = JSON.parse('[' + content + ']');

    jsonArray.forEach((parsed, index) => {
      // Identify document type based on content
      let docType = `doc_${index}`;

      if (parsed.InvoiceNo === 'RSNT26T0147') {
        docType = 'RSNT26T0147 - Arcsys';
      } else if (parsed.PurchaseOrderNo === 'FLCN26PO024') {
        docType = 'FLCN26PO024 - Huhtamaki V2';
      } else if (parsed.DeliveryNoteNo === 'RSNT26J0018') {
        docType = 'RSNT26J0018 - Resonate';
      } else if (parsed.DeliveryChallan === 'RSNT26D0127') {
        docType = 'RSNT26D0127 - Ingram 32';
      } else if (parsed.IRN && parsed.IRN.includes('398b80dc39ea3bafadfd629bca45d20d3dc8d1a12546afbcd0e1d743d883cb4d')) {
        docType = 'RSNT26T0129 - Ingram 29';
      } else if (parsed.DeliveryDetails && parsed.DeliveryDetails.InvoiceNo === 'RSNT26T0122') {
        docType = 'RSNT26T0122 - Diligent Solutions';
      } else if (parsed.DeliveryDetails && parsed.DeliveryDetails.DeliveryNoteNo === 'RSNT26J0022') {
        docType = 'RSNT26J0022 - Resonate';
      } else if (parsed.PurchaseOrder && parsed.PurchaseOrder.PO_Number === '66-G3474') {
        docType = 'IAPO_66-G3474_20250718_141420';
      } else if (parsed.PurchaseOrder && parsed.PurchaseOrder.PO_Number === 'BAL-EGB-ISP--J&K/PUR/10000541') {
        docType = '2.PO_3852_10000541_0_US';
      } else if (parsed.document_type === 'Tax Invoice' && parsed.total_amount === 22732.41) {
        docType = 'RSNT_SALES_INGRAM';
      } else if (parsed.document_type === 'Delivery Note' && parsed.delivery_note_no && parsed.delivery_note_no.includes('RSNT26D03')) {
        docType = 'Delivery Voucher';
      }

      expectedOutputs[docType] = parsed;
      console.log(`✅ Loaded expected output for ${docType}`);
    });
    
    return expectedOutputs;
  } catch (error) {
    console.error('Failed to load expected JSON:', error.message);
    return {};
  }
}

// Get all PDF files from example folder
function getPDFFiles() {
  try {
    const files = fs.readdirSync(EXAMPLE_FOLDER);
    return files.filter(file => file.toLowerCase().endsWith('.pdf'));
  } catch (error) {
    console.error('Failed to read example folder:', error.message);
    return [];
  }
}

// Extract text from PDF
async function extractPDFText(pdfPath) {
  try {
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdf(dataBuffer, {
      normalizeWhitespace: true,
      disableCombineTextItems: false,
    });
    return data.text;
  } catch (error) {
    console.error(`Failed to extract text from ${pdfPath}:`, error.message);
    return null;
  }
}

// Compare extracted data with expected data
function compareResults(extracted, expected) {
  const differences = [];

  function compareObjects(extractedObj, expectedObj, path = '') {
    if (!extractedObj || !expectedObj) {
      if (extractedObj !== expectedObj) {
        differences.push({
          path: path || 'root',
          expected: expectedObj,
          extracted: extractedObj,
          type: 'missing_or_extra',
        });
      }
      return;
    }

    // Handle arrays
    if (Array.isArray(expectedObj)) {
      if (!Array.isArray(extractedObj)) {
        differences.push({
          path,
          expected: expectedObj,
          extracted: extractedObj,
          type: 'type_mismatch',
        });
        return;
      }

      if (expectedObj.length !== extractedObj.length) {
        differences.push({
          path,
          expected: `Array with ${expectedObj.length} items`,
          extracted: `Array with ${extractedObj.length} items`,
          type: 'array_length_mismatch',
        });
      }

      expectedObj.forEach((expectedItem, index) => {
        if (extractedObj[index]) {
          compareObjects(extractedObj[index], expectedItem, `${path}[${index}]`);
        }
      });
      return;
    }

    // Handle objects
    if (typeof expectedObj === 'object') {
      if (typeof extractedObj !== 'object' || Array.isArray(extractedObj)) {
        differences.push({
          path,
          expected: expectedObj,
          extracted: extractedObj,
          type: 'type_mismatch',
        });
        return;
      }

      // Check all expected keys
      Object.keys(expectedObj).forEach(key => {
        const expectedValue = expectedObj[key];
        const extractedValue = extractedObj[key];

        if (extractedValue === undefined) {
          differences.push({
            path: path ? `${path}.${key}` : key,
            expected: expectedValue,
            extracted: 'MISSING',
            type: 'missing_field',
          });
        } else if (typeof expectedValue === 'object' && expectedValue !== null) {
          compareObjects(extractedValue, expectedValue, path ? `${path}.${key}` : key);
        } else if (extractedValue !== expectedValue) {
          differences.push({
            path: path ? `${path}.${key}` : key,
            expected: expectedValue,
            extracted: extractedValue,
            type: 'value_mismatch',
          });
        }
      });

      return;
    }

    // Handle primitive values
    if (extractedObj !== expectedObj) {
      differences.push({
        path: path || 'root',
        expected: expectedObj,
        extracted: extractedObj,
        type: 'value_mismatch',
      });
    }
  }

  compareObjects(extracted, expected);
  return differences;
}

// Find matching expected output for a PDF
function findMatchingExpected(pdfName, expectedOutputs) {
  const pdfLower = pdfName.toLowerCase();

  // Try to match based on PDF name patterns
  if (pdfLower.includes('arcsys') || pdfLower.includes('rsnt26t0147')) {
    return expectedOutputs['RSNT26T0147 - Arcsys'];
  }
  if (pdfLower.includes('huhtamaki') || pdfLower.includes('flcn26po024')) {
    return expectedOutputs['FLCN26PO024 - Huhtamaki V2'];
  }
  if (pdfLower.includes('resonate') && pdfLower.includes('rsnt26j0018')) {
    return expectedOutputs['RSNT26J0018 - Resonate'];
  }
  if (pdfLower.includes('ingram') && pdfLower.includes('32') || pdfLower.includes('rsnt26d0127')) {
    return expectedOutputs['RSNT26D0127 - Ingram 32'];
  }
  if (pdfLower.includes('ingram') && pdfLower.includes('29') || pdfLower.includes('rsnt26t0129')) {
    return expectedOutputs['RSNT26T0129 - Ingram 29'];
  }
  if (pdfLower.includes('diligent') || pdfLower.includes('rsnt26t0122')) {
    return expectedOutputs['RSNT26T0122 - Diligent Solutions'];
  }
  if (pdfLower.includes('delivery voucher')) {
    return expectedOutputs['RSNT26D0127 - Ingram 32']; // Same as Ingram 32
  }
  if (pdfLower.includes('sales')) {
    return expectedOutputs['RSNT26T0147 - Arcsys']; // Use similar structure
  }
  if (pdfLower.includes('iapo')) {
    return expectedOutputs['FLCN26PO024 - Huhtamaki V2']; // Use similar PO structure
  }
  if (pdfLower.includes('po_3852')) {
    return expectedOutputs['FLCN26PO024 - Huhtamaki V2']; // Use similar PO structure
  }
  if (pdfLower.includes('rsnt26j0022')) {
    return expectedOutputs['RSNT26J0022 - Resonate']; // Use Job Order structure
  }

  // If no specific match, return the first available
  return Object.values(expectedOutputs)[0];
}

// Run comprehensive test
async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive PDF Parser Test\n');

  // Load expected outputs
  console.log('📋 Loading expected JSON outputs...');
  const expectedOutputs = loadExpectedOutput();
  console.log(`✅ Loaded ${Object.keys(expectedOutputs).length} expected outputs\n`);

  // Get PDF files
  console.log('📁 Scanning for PDF files...');
  const pdfFiles = getPDFFiles();
  console.log(
    `✅ Found ${pdfFiles.length} PDF files:`,
    pdfFiles.map(f => f.replace('.pdf', '')).join(', '),
    '\n'
  );

  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  const detailedResults = [];

  // Test each PDF
  for (const pdfFile of pdfFiles) {
    totalTests++;
    console.log(`\n📄 Testing: ${pdfFile}`);
    console.log('─'.repeat(50));

    try {
      // Extract text from PDF
      const pdfPath = path.join(EXAMPLE_FOLDER, pdfFile);
      const pdfText = await extractPDFText(pdfPath);

      if (!pdfText) {
        console.log('❌ Failed to extract text from PDF');
        failedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'FAILED',
          error: 'Text extraction failed',
        });
        continue;
      }

      // Parse with our parser
      const extractedData = ComprehensivePDFParser.parseDocument(pdfText);

      // Find matching expected output
      const expectedData = findMatchingExpected(pdfFile, expectedOutputs);

      if (!expectedData) {
        console.log('⚠️  No matching expected output found');
        detailedResults.push({
          pdf: pdfFile,
          status: 'NO_EXPECTED_DATA',
          extracted: extractedData,
        });
        continue;
      }

      // Compare results
      const differences = compareResults(extractedData, expectedData, pdfFile);

      if (differences.length === 0) {
        console.log('✅ PASSED - All fields match expected output');
        passedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'PASSED',
          extracted: extractedData,
        });
      } else {
        console.log(`❌ FAILED - ${differences.length} differences found:`);
        differences.forEach(diff => {
          console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
        });
        failedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'FAILED',
          differences,
          extracted: extractedData,
          expected: expectedData,
        });
      }
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      failedTests++;
      detailedResults.push({
        pdf: pdfFile,
        status: 'ERROR',
        error: error.message,
      });
    }
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (failedTests > 0) {
    console.log('\n🔍 DETAILED FAILURES:');
    detailedResults
      .filter(result => result.status === 'FAILED')
      .forEach(result => {
        console.log(`\n📄 ${result.pdf}:`);
        result.differences.forEach(diff => {
          console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
        });
      });
  }

  // Save detailed results to file
  const resultsFile = 'test-results.json';
  fs.writeFileSync(resultsFile, JSON.stringify(detailedResults, null, 2));
  console.log(`\n📝 Detailed results saved to: ${resultsFile}`);

  return {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    successRate: (passedTests / totalTests) * 100,
    detailedResults,
  };
}

// Run the test
if (require.main === module) {
  runComprehensiveTest()
    .then(results => {
      console.log('\n🎯 Test completed!');
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('💥 Test failed with error:', error);
      process.exit(1);
    });
}

module.exports = { runComprehensiveTest };