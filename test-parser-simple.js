const fs = require('fs');
const path = require('path');
const pdf = require('pdf-parse');
const { ComprehensivePDFParser } = require('./lib/comprehensive-pdf-parser.js');

// Test configuration
const EXAMPLE_FOLDER = './example file';

// Hardcoded expected outputs based on the expected JSON file
const EXPECTED_OUTPUTS = {
  'arcsys_invoice': {
    'InvoiceNo': 'RSNT26T0147',
    'InvoiceDate': '23-Jul-25',
    'DeliveryNote': 'RSNT26D0147',
    'DeliveryNoteDate': '22-Jul-25',
    'Seller': {
      'Name': 'Resonate Systems Private Limited',
      'Address': 'R2, First Floor, 31/6, Thayappa Garden, Bilekahalli, Bangalore, KA, 560076',
      'GSTIN': '29**********1ZB',
      'PAN': '**********',
      'Email': '<EMAIL>',
      'BankDetails': {
        'BankName': 'HSBC Bank',
        'AccountNumber': '************',
        'BranchIFSC': 'MG Road & HSBC0560002',
      },
    },
    'Buyer': {
      'Name': 'Arcsys Techsolutions Private Limited',
      'Address':
        'FLOOR 2ND, FLAT NO 13, BLK-C, PKT-4, SECTOR-5, NEAR RITHALA, Rohini Sector 5, New Delhi, North West Delhi, Delhi, 110085',
      'GSTIN': '07**********1Z6',
      'PAN': '**********',
    },
    'DispatchDetails': {
      'DispatchedThrough': 'Safeexpress',
      'Destination': 'Delhi',
      'PaymentTerms': '30 Days',
    },
    'Items': [
      {
        'Description':
          'RSNT-RUPS-CRU12V2A - RESONATE RouterUPS - Purpose Built, Power Backup for Wi-Fi Router, Upto 4 Hours Backup, 30 Seconds Installation, Compatible with all 12V<2A Routers',
        'HSN/SAC': '********',
        'Quantity': 10.0,
        'Unit': 'NOS',
        'Rate': 950.0,
        'Amount': 9500.0,
      },
    ],
    'Tax': {
      'IGST': {
        'Rate': '18%',
        'Amount': 1710.0,
      },
    },
    'TotalAmount': 11210.0,
    'AmountInWords': 'INR Eleven Thousand Two Hundred Ten Only',
    'Warranty': '1 year from the date of goods sold',
    'SupportEmail': '<EMAIL>',
    'Jurisdiction': 'Bangalore',
  },
  'huhtamaki_po': {
    'PurchaseOrderNo': 'FLCN26PO024',
    'Date': '14-Jul-25',
    'Buyer': {
      'Name': 'Falconn ESDM Private Limited',
      'Address': 'R4, Ground Floor, 31/5, Thayappa Garden, Bilekahalli, Bangalore - 560076',
      'GSTIN': '29**********1Z5',
      'Email': '<EMAIL>',
    },
    'Supplier': {
      'Name': 'HUHTAMAKI INDIA LIMITED',
      'Address':
        'PLOT NO 155,154,32 AND PART 31, BOMMASANDRA, JIGANI LINK ROAD, ANEKAL, BENGALURU, Karnataka, 560105',
      'GSTIN': '29**********1ZH',
      'PAN': '**********',
    },
    'Items': [
      {
        'Description': 'QR Code Labels',
        'Amount': 20250.0,
        'Rate': 0.9,
        'Quantity': 22500.0,
        'Unit': 'Nos',
      },
      {
        'Description': 'CRU12V2AU (Micro) QR Code Label-CRU12V3A',
        'Amount': 12000.0,
        'Rate': 1.2,
        'Quantity': 10000.0,
        'Unit': 'Nos',
      },
    ],
    'Taxes': {
      'CGST': 2750.62,
      'SGST': 2750.62,
    },
    'TotalAmount': 37751.24,
    'AmountInWords': 'INR Thirty Seven Thousand Seven Hundred Fifty One and Twenty Four paise Only',
  },
  'resonate_delivery_0018': {
    'DeliveryNoteNo': 'RSNT26J0018',
    'Date': '3-Jul-25',
    'Seller': {
      'Name': 'Resonate Systems Private Limited',
      'Address': 'R2, First Floor, 31/6, Thayappa Garden, Bilekahalli, Bangalore, KA, 560076',
      'GSTIN': '29**********1ZB',
      'PAN': '**********',
      'Email': '<EMAIL>',
    },
    'Buyer': {
      'Name': 'Falconn ESDM Private Limited',
      'Address': 'R4, Ground Floor, 31/5, Thayappa Garden, Bilekahalli, Bangalore - 560076',
      'GSTIN': '29**********1Z5',
      'PAN': '**********',
    },
    'Items': [
      {
        'Description': 'RSNT-RUPS-CRU12V2A-GEN2-RMA',
        'Quantity': 1.0,
        'Unit': 'NOS',
        'HSN/SAC': '********',
      },
      {
        'Description': 'RSNT-RUPS-CRU12V2A-RMA',
        'Quantity': 1.0,
        'Unit': 'NOS',
        'HSN/SAC': '********',
      },
    ],
    'TotalQuantity': 2.0,
    'Remarks': 'Recd. in Good Condition',
  },
  'ingram_delivery_32': {
    'DeliveryChallan': 'RSNT26D0127',
    'Company': {
      'Name': 'Resonate Systems Private Limited',
      'Address': 'R2, First Floor, 31/6, Thayappa Garden, Bilekahalli,Bangalore,KA,560076',
      'GSTIN': '29**********1ZB',
      'State': 'Karnataka',
      'StateCode': '29',
      'Email': '<EMAIL>',
      'PAN': '**********',
    },
    'Consignee': {
      'Name': 'INGRAM MICRO INDIA PRIVATE LIMITED - 32',
      'Address':
        'INGRAM MICRO INDIA PRIVATE LIMITED - 32 PLOT# 35, WAREHOUSING CENTRE, BUILDING# 38/748, GANDHI NAGAR, KADAVANTHRA, COCHIN 682020',
      'GSTIN': '32**********1ZW',
      'PAN': '**********',
    },
    'Buyer': {
      'Name': 'INGRAM MICRO INDIA PRIVATE LIMITED - 32',
      'Address':
        'INGRAM MICRO INDIA PRIVATE LIMITED - 32 PLOT# 35, WAREHOUSING CENTRE, BUILDING# 38/748, GANDHI NAGAR, KADAVANTHRA, COCHIN 682020',
      'GSTIN': '32**********1ZW',
      'PAN': '**********',
    },
    'DeliveryDetails': {
      'DeliveryNoteNo': 'RSNT26D0127',
      'ReferenceNoAndDate': '17-C3046 dt. 2-Jul-25',
      'BuyersOrderNo': '17-C3046',
      'DispatchDocNo': 'RSNT26D0127',
      'DispatchedThrough': 'Safexpress',
      'DispatchDate': '4-Jul-25',
      'PaymentTerms': '45 Days Other References Dated 2',
      'OtherReferencesDate': '2-Jul-25',
      'Destination': 'Cochin',
      'TermsOfDelivery': '',
    },
    'Goods': [
      {
        'Description': 'RSNT-RUPS-CRU12V2AU',
        'Quantity': 20.0,
        'Unit': 'NOS',
        'HSN_SAC': '********',
        'Details': 'RESONATE RouterUPS - Purpose Built, Power Backup for Wi-Fi Router,',
        'Tax': 'IGST @ 18%',
      },
    ],
    'TotalQuantity': '20.00 NOS',
    'Jurisdiction': 'Bangalore',
    'DocumentNote': 'This is a Computer Generated Document',
    'Signature': 'Authorised Signatory',
    'Condition': 'Recd. in Good Condition',
    'E_O_E': true,
  },
  'ingram_invoice_29': {
    'IRN': '398b80dc39ea3bafadfd629bca45d20d3dc8d1a12546afbcd0e1d743d883cb4d',
    'AckNo': '112525762563856',
    'AckDate': '9-Jul-25',
    'Company': {
      'Name': 'Resonate Systems Private Limited',
      'Address': 'R2, First Floor, 31/6, Thayappa Garden, Bilekahalli,Bangalore,KA,560076',
      'GSTIN': '29**********1ZB',
      'State': 'Karnataka',
      'StateCode': '29',
      'Email': '<EMAIL>',
      'PAN': '**********',
    },
    'Consignee': {
      'Name': 'INGRAM MICRO INDIA PRIVATE LIMITED',
      'Address': 'S.No.196/2, Hulahalli, Jigani Hobli, Anekal, CK Palya, Bangalore - 560083',
      'GSTIN': '29**********1ZJ',
      'PAN': '**********',
    },
    'Buyer': {
      'Name': 'INGRAM MICRO INDIA PRIVATE LIMITED',
      'Address': 'S.No.196/2, Hulahalli, Jigani Hobli, Anekal, CK Palya, Bangalore - 560083',
      'GSTIN': '29**********1ZJ',
      'PAN': '**********',
    },
    'DeliveryDetails': {
      'InvoiceNo': 'RSNT26T0129',
      'ReferenceNoAndDate': '17-C3046 dt. 2-Jul-25',
      'BuyersOrderNo': '17-C3046',
      'DispatchDocNo': 'RSNT26D0129',
      'DispatchedThrough': 'Safexpress',
      'DispatchDate': '4-Jul-25',
      'PaymentTerms': '45 Days Other References Dated 2',
      'OtherReferencesDate': '2-Jul-25',
      'Destination': 'Bangalore',
      'TermsOfDelivery': '',
    },
    'Goods': [
      {
        'Description': 'RSNT-RUPS-CRU12V2AU',
        'Quantity': 20.0,
        'Unit': 'NOS',
        'HSN_SAC': '********',
        'Details': 'RESONATE RouterUPS - Purpose Built, Power Backup for Wi-Fi Router,',
        'Tax': 'IGST @ 18%',
      },
    ],
    'TotalQuantity': '20.00 NOS',
    'Jurisdiction': 'Bangalore',
    'DocumentNote': 'This is a Computer Generated Document',
    'Signature': 'Authorised Signatory',
    'Condition': 'Recd. in Good Condition',
    'E_O_E': true,
  },
};

// Get all PDF files from example folder
function getPDFFiles() {
  try {
    const files = fs.readdirSync(EXAMPLE_FOLDER);
    return files.filter(file => file.toLowerCase().endsWith('.pdf'));
  } catch (error) {
    console.error('Failed to read example folder:', error.message);
    return [];
  }
}

// Extract text from PDF
async function extractPDFText(pdfPath) {
  try {
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdf(dataBuffer, {
      normalizeWhitespace: true,
      disableCombineTextItems: false,
    });
    return data.text;
  } catch (error) {
    console.error(`Failed to extract text from ${pdfPath}:`, error.message);
    return null;
  }
}

// Compare extracted data with expected data
function compareResults(extracted, expected) {
  const differences = [];

  function compareObjects(extractedObj, expectedObj, path = '') {
    if (!extractedObj || !expectedObj) {
      if (extractedObj !== expectedObj) {
        differences.push({
          path: path || 'root',
          expected: expectedObj,
          extracted: extractedObj,
          type: 'missing_or_extra',
        });
      }
      return;
    }

    // Handle arrays
    if (Array.isArray(expectedObj)) {
      if (!Array.isArray(extractedObj)) {
        differences.push({
          path,
          expected: expectedObj,
          extracted: extractedObj,
          type: 'type_mismatch',
        });
        return;
      }

      if (expectedObj.length !== extractedObj.length) {
        differences.push({
          path,
          expected: `Array with ${expectedObj.length} items`,
          extracted: `Array with ${extractedObj.length} items`,
          type: 'array_length_mismatch',
        });
      }

      expectedObj.forEach((expectedItem, index) => {
        if (extractedObj[index]) {
          compareObjects(extractedObj[index], expectedItem, `${path}[${index}]`);
        }
      });
      return;
    }

    // Handle objects
    if (typeof expectedObj === 'object') {
      if (typeof extractedObj !== 'object' || Array.isArray(extractedObj)) {
        differences.push({
          path,
          expected: expectedObj,
          extracted: extractedObj,
          type: 'type_mismatch',
        });
        return;
      }

      // Check all expected keys
      Object.keys(expectedObj).forEach(key => {
        const expectedValue = expectedObj[key];
        const extractedValue = extractedObj[key];

        if (extractedValue === undefined) {
          differences.push({
            path: path ? `${path}.${key}` : key,
            expected: expectedValue,
            extracted: 'MISSING',
            type: 'missing_field',
          });
        } else if (typeof expectedValue === 'object' && expectedValue !== null) {
          compareObjects(extractedValue, expectedValue, path ? `${path}.${key}` : key);
        } else if (extractedValue !== expectedValue) {
          differences.push({
            path: path ? `${path}.${key}` : key,
            expected: expectedValue,
            extracted: extractedValue,
            type: 'value_mismatch',
          });
        }
      });

      return;
    }

    // Handle primitive values
    if (extractedObj !== expectedObj) {
      differences.push({
        path: path || 'root',
        expected: expectedObj,
        extracted: extractedObj,
        type: 'value_mismatch',
      });
    }
  }

  compareObjects(extracted, expected);
  return differences;
}

// Find matching expected output for a PDF
function findMatchingExpected(pdfName) {
  const pdfLower = pdfName.toLowerCase();

  // Try to match based on PDF name patterns
  if (pdfLower.includes('arcsys')) {
    return EXPECTED_OUTPUTS['arcsys_invoice'];
  }
  if (pdfLower.includes('huhtamaki')) {
    return EXPECTED_OUTPUTS['huhtamaki_po'];
  }
  if (pdfLower.includes('resonate') && pdfLower.includes('0018')) {
    return EXPECTED_OUTPUTS['resonate_delivery_0018'];
  }
  if (pdfLower.includes('ingram') && pdfLower.includes('32')) {
    return EXPECTED_OUTPUTS['ingram_delivery_32'];
  }
  if (pdfLower.includes('ingram') && pdfLower.includes('29')) {
    return EXPECTED_OUTPUTS['ingram_invoice_29'];
  }
  if (pdfLower.includes('diligent')) {
    return EXPECTED_OUTPUTS['arcsys_invoice']; // Use similar structure
  }
  if (pdfLower.includes('delivery voucher')) {
    return EXPECTED_OUTPUTS['ingram_delivery_32']; // Same as Ingram 32
  }
  if (pdfLower.includes('sales')) {
    return EXPECTED_OUTPUTS['arcsys_invoice']; // Use similar structure
  }
  if (pdfLower.includes('iapo')) {
    return EXPECTED_OUTPUTS['huhtamaki_po']; // Use similar PO structure
  }
  if (pdfLower.includes('po_3852')) {
    return EXPECTED_OUTPUTS['huhtamaki_po']; // Use similar PO structure
  }

  // If no specific match, return the first available
  return Object.values(EXPECTED_OUTPUTS)[0];
}

// Run comprehensive test
async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive PDF Parser Test\n');

  console.log('📋 Loaded expected JSON outputs...');
  console.log(`✅ Loaded ${Object.keys(EXPECTED_OUTPUTS).length} expected outputs\n`);

  // Get PDF files
  console.log('📁 Scanning for PDF files...');
  const pdfFiles = getPDFFiles();
  console.log(
    `✅ Found ${pdfFiles.length} PDF files:`,
    pdfFiles.map(f => f.replace('.pdf', '')).join(', '),
    '\n'
  );

  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  const detailedResults = [];

  // Test each PDF
  for (const pdfFile of pdfFiles) {
    totalTests++;
    console.log(`\n📄 Testing: ${pdfFile}`);
    console.log('─'.repeat(50));

    try {
      // Extract text from PDF
      const pdfPath = path.join(EXAMPLE_FOLDER, pdfFile);
      const pdfText = await extractPDFText(pdfPath);

      if (!pdfText) {
        console.log('❌ Failed to extract text from PDF');
        failedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'FAILED',
          error: 'Text extraction failed',
        });
        continue;
      }

      // Parse with our parser
      const extractedData = ComprehensivePDFParser.parseDocument(pdfText);

      // Find matching expected output
      const expectedData = findMatchingExpected(pdfFile);

      if (!expectedData) {
        console.log('⚠️  No matching expected output found');
        detailedResults.push({
          pdf: pdfFile,
          status: 'NO_EXPECTED_DATA',
          extracted: extractedData,
        });
        continue;
      }

      // Compare results
      const differences = compareResults(extractedData, expectedData);

      if (differences.length === 0) {
        console.log('✅ PASSED - All fields match expected output');
        passedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'PASSED',
          extracted: extractedData,
        });
      } else {
        console.log(`❌ FAILED - ${differences.length} differences found:`);
        differences.forEach(diff => {
          console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
        });
        failedTests++;
        detailedResults.push({
          pdf: pdfFile,
          status: 'FAILED',
          differences,
          extracted: extractedData,
          expected: expectedData,
        });
      }
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
      failedTests++;
      detailedResults.push({
        pdf: pdfFile,
        status: 'ERROR',
        error: error.message,
      });
    }
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (failedTests > 0) {
    console.log('\n🔍 DETAILED FAILURES:');
    detailedResults
      .filter(result => result.status === 'FAILED')
      .forEach(result => {
        console.log(`\n📄 ${result.pdf}:`);
        result.differences.forEach(diff => {
          console.log(`   ${diff.path}: Expected "${diff.expected}", Got "${diff.extracted}"`);
        });
      });
  }

  // Save detailed results to file
  const resultsFile = 'test-results-simple.json';
  fs.writeFileSync(resultsFile, JSON.stringify(detailedResults, null, 2));
  console.log(`\n📝 Detailed results saved to: ${resultsFile}`);

  return {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    successRate: (passedTests / totalTests) * 100,
    detailedResults,
  };
}

// Run the test
if (require.main === module) {
  runComprehensiveTest()
    .then(results => {
      console.log('\n🎯 Test completed!');
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('💥 Test failed with error:', error);
      process.exit(1);
    });
}

module.exports = { runComprehensiveTest };
