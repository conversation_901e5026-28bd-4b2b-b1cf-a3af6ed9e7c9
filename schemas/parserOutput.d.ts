/**
 * Parser Output Schema Definitions
 * 
 * This file defines the canonical structure for parsed invoice/document data.
 * These interfaces provide type safety and IntelliSense support for the parser output.
 */

// Base types for common data structures
export interface Address {
  street?: string;
  street2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  formatted?: string; // Full formatted address string
}

export interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  fax?: string;
  website?: string;
}

export interface Entity extends ContactInfo {
  companyName?: string;
  taxId?: string;
  vatNumber?: string;
  registrationNumber?: string;
  address?: Address;
}

// Main entity interfaces
export interface Buyer extends Entity {
  customerId?: string;
  accountNumber?: string;
}

export interface Seller extends Entity {
  vendorId?: string;
  bankDetails?: BankDetails;
}

export interface Consignee extends Entity {
  reference?: string;
  instructions?: string;
}

export interface BankDetails {
  bankName?: string;
  accountNumber?: string;
  routingNumber?: string;
  iban?: string;
  swiftCode?: string;
  sortCode?: string;
}

// Item and pricing structures
export interface LineItem {
  id?: string;
  description: string;
  productCode?: string;
  sku?: string;
  quantity: number;
  unitOfMeasure?: string;
  unitPrice: number;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  netAmount: number;
  taxAmount?: number;
  grossAmount: number;
  taxRate?: number;
  taxCode?: string;
}

export interface Tax {
  type: string; // e.g., 'VAT', 'GST', 'Sales Tax'
  rate: number; // Percentage rate
  amount: number;
  taxableAmount?: number;
  description?: string;
  code?: string;
}

export interface Totals {
  subtotal: number; // Total before taxes and discounts
  totalDiscount?: number;
  totalTax: number;
  total: number; // Final amount due
  amountPaid?: number;
  amountDue?: number;
  currency: string; // ISO currency code (e.g., 'USD', 'EUR')
}

// Date-related structures
export interface Dates {
  invoiceDate?: string; // ISO date string
  dueDate?: string;
  shipDate?: string;
  deliveryDate?: string;
  orderDate?: string;
  paymentDate?: string;
  createdDate?: string;
  modifiedDate?: string;
}

// Delivery and shipping information
export interface Delivery {
  method?: string; // e.g., 'Standard', 'Express', 'Overnight'
  carrier?: string; // e.g., 'FedEx', 'UPS', 'DHL'
  trackingNumber?: string;
  estimatedDelivery?: string; // ISO date string
  actualDelivery?: string; // ISO date string
  deliveryAddress?: Address;
  shippingCost?: number;
  instructions?: string;
}

// Payment information
export interface PaymentTerms {
  terms?: string; // e.g., 'Net 30', '2/10 Net 30'
  daysNet?: number;
  discountPercent?: number;
  discountDays?: number;
  lateFee?: number;
  method?: string; // e.g., 'Check', 'Wire Transfer', 'Credit Card'
}

// Document metadata
export interface DocumentMetadata {
  documentType: 'invoice' | 'quote' | 'purchase_order' | 'receipt' | 'credit_note' | 'other';
  documentNumber: string;
  referenceNumber?: string;
  poNumber?: string; // Purchase Order Number
  quoteNumber?: string;
  pages?: number;
  language?: string;
  originalFilename?: string;
  processingTimestamp?: string; // ISO timestamp
  confidence?: number; // Parser confidence score (0-1)
}

// Additional fields for flexibility
export interface CustomFields {
  [key: string]: string | number | boolean | null;
}

// Main parser output interface
export interface ParserOutput {
  // Document identification
  metadata: DocumentMetadata;
  
  // Core entities
  buyer?: Buyer;
  seller: Seller; // Seller is typically required
  consignee?: Consignee;
  
  // Financial data
  items: LineItem[];
  taxes: Tax[];
  totals: Totals;
  
  // Temporal information
  dates: Dates;
  
  // Logistics
  delivery?: Delivery;
  
  // Payment information
  paymentTerms?: PaymentTerms;
  
  // Additional structured data
  notes?: string;
  specialInstructions?: string;
  
  // Extensibility for custom fields
  customFields?: CustomFields;
  
  // Raw extracted text for reference
  rawText?: string;
  
  // Validation and quality indicators
  validationErrors?: string[];
  warnings?: string[];
  extractionQuality?: 'high' | 'medium' | 'low';
}

// Utility types for validation and testing
export type RequiredFields = 'metadata' | 'seller' | 'items' | 'totals' | 'dates';
export type OptionalFields = Exclude<keyof ParserOutput, RequiredFields>;

// Helper type for partial updates during parsing
export type PartialParserOutput = Partial<ParserOutput> & Pick<ParserOutput, 'metadata'>;

// Export type guards for runtime validation
export interface ValidationSchema {
  isValidParserOutput(data: unknown): data is ParserOutput;
  isValidLineItem(data: unknown): data is LineItem;
  isValidTotals(data: unknown): data is Totals;
  isValidEntity(data: unknown): data is Entity;
}
